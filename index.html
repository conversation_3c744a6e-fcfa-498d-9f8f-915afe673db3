<!doctype html>
<html lang="en">
  <head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

<style type="text/css">
body{
  background: rgb(255,255,255);
  background: -moz-radial-gradient(center, ellipse cover, rgba(255,255,255,1) 0%, rgba(246,246,246,1) 47%, rgba(237,237,237,1) 100%); /* FF3.6-15 */
  background: -webkit-radial-gradient(center, ellipse cover, rgba(255,255,255,1) 0%,rgba(246,246,246,1) 47%,rgba(237,237,237,1) 100%); /* Chrome10-25,Safari5.1-6 */
  background: radial-gradient(ellipse at center, rgba(255,255,255,1) 0%,rgba(246,246,246,1) 47%,rgba(237,237,237,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ededed',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}
.chsone-wrap{
    height: 100vh;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.display-4{font-size: 2rem;}
.logo{
  width: 300px;
  margin: 0px auto;
  z-index: 999;
}
.bg-text{
  font-size: 13vw;
}
.shapes-wrap .shape{
  background: #ff9f19;
  position: absolute;
    transform: scale(1.0) skew(-35deg);
  -webkit-animation: zoom 2s ease-out alternate;
    animation: zoom 2s ease-out alternate;
}

.shapes-wrap .shape_1{
  width: 150px;
  height: 150px;
  top: 0px;
  left: 25%;
}
.shapes-wrap .shape_2{
  width: 250px;
  height: 350px;
  top: 0px;
  left: 65%;
}
.shapes-wrap .shape_3{
  width: 150px;
  height: 100px;
  bottom: 0px;
  left: 20%;
}
.shapes-wrap .shape_4{
  width: 250px;
  height: 250px;
  bottom: 0px;
  left: 65%;
}
@-webkit-keyframes zoom {
    0% {
        -webkit-transform: scale(10.0) skew(-35deg);
    }
    100% {
        -webkit-transform: scale(1.0) skew(-35deg);
    }
}

@keyframes zoom {
    0% {
        transform: scale(10.0) skew(-35deg);
    }
    100% {
        transform: scale(1.0) skew(-35deg);
    }
}
</style>

<title>Under Maintenence</title>

</head>
<body>
  <div class="shapes-wrap">
    <div class="shape shape_1"></div>
    <div class="shape shape_2"></div>
    <div class="shape shape_3"></div>
    <div class="shape shape_4"></div>
  </div>

  <div class="chsone-wrap">
      <div class="logo"><img src="http://chsone.in/app/public/assets/img/logo.png" class="img-fluid" alt=""></div>
      <div class="text-wrap">
        <h2 class="display-4">CHSONE Under Maintenence</h2>
      </div>
  </div>

  </body>
</html>
