<?php
/**
 * This class file is a Listener Common Billing.
 *
 * PHP versions 5.5.9
 *
 * Project name CHSONE
 * @version 2: common/IncomeAccount/Listener/CommomBillingListener 2016-02-03 $
 * @copyright Copyright (C) 2016 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2016 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category CommomBillingListener controller
 * <AUTHOR> <Futurescape Technologies>
 * @since File available since Release 1.0
 */
namespace ChsOne\Components\IncomeTracker\Setting\Listeners;

use ChsOne\Models\GrpLedgTree;
use ChsOne\Models\IncomeCommonBilling;
use ChsOne\Models\SocUnitsTpl;
use ChsOne\Components\Accounts\Accnt;
use ChsOne\Models\IncomeAccounts;

class CommonBillingListener {
    use \ChsOne\Components\Traits\DateFormatTrait;
    public function getAllUnitType($event, $component, $data=array())
    {
        global $di,$config, $constants;
        $this->di = $di;
        $this->config = $config;
        $this->constants = $constants;
        
        $arrUnitTypes = $this->constants['reserved_units'];
        unset($arrUnitTypes['Parking']);
        $reserveAreaUnittype = array_keys($arrUnitTypes);
        $soc_id = $data['auth']['soc_id'];
        $conditions = "soc_id = ?1 AND status = ?2 ";
        $parameters = array("1" => $soc_id, "2" => '1');
        
        switch($data['selectperticular']){
            case 'parking': 
                $conditions .= " and soc_unit_type = ?3";
                $parameters["3"] = 'parking';
            break;
            case 'notparking':
                $conditions .= " and soc_unit_type in ('".implode("','", $reserveAreaUnittype)."')";
            break;
            
        default :
            $conditions .= "";
        }
        //print_r($conditions);
        $objallsocietyunits = SocUnitsTpl::find(array(
            $conditions,
            "bind" => $parameters
        ));
        
        $arrsocietyunit = array();
        if(!empty($objallsocietyunits)){
            $arrallsocietyunits = $objallsocietyunits->toArray();
            
            foreach($arrallsocietyunits as $key => $societyunitdetails){
                $arrsocietyunit[$societyunitdetails['soc_units_type_id']] = $societyunitdetails['type'];
            }
        }
        return $arrsocietyunit;
    }
    
    public function getOnetimeParticulars($event, $component, $data=array())
    {
        global $di;
        $this->di = $di;
        $soc_id = $data['auth']['soc_id'];
        $effective_date = date("Y-m-d");
        $IncomeCommonBilling = new IncomeCommonBilling();
        $recordesOnetime = $IncomeCommonBilling->getCommonAreaRent($soc_id, $effective_date, 'onetime');
        return $recordesOnetime;
    }
    
    /*public function getOtherParticulars($event, $component, $data=array())
    {
        global $di;
        $this->di = $di;
        $soc_id = $data['auth']['soc_id'];
        $effective_date = $this->getCurrentDate('database');
        $IncomeCommonBilling = new IncomeCommonBilling();
        $arrCommonAreaRentalDuration = $data['arrCommonAreaRentalDuration'];
        $recordesallOther = array();
        foreach ($arrCommonAreaRentalDuration as $irentalduration => $commonarerentaldurationdetails){
            $recordesallOther[$irentalduration] = $IncomeCommonBilling->getCommonAreaRent($soc_id,$effective_date, $irentalduration);
        }
        
        return $recordesallOther;
    }*/
    
    
    public function savecommonarearent($event, $component, $data=array())
    { 
        $auth = $data['auth'];
        $arrPostdata = $data['arrPostdata'];
        //echo '<pre>';
        $error_particular = array();
        $today = $this->getCurrentDate('database');
        //echo '<pre>'; print_r($arrPostdata);exit;
        for($i = 1 ; $i <= $arrPostdata['totalonetime']; $i++)
        {
            $err = false;
            if($arrPostdata['particular_'.$i] != '') {
//                echo '<pre>'; print_r($arrPostdata); exit;
                $particular     = $arrPostdata['particular_'.$i];
                $rent           = $arrPostdata['rent_'.$i];
                $rent_type      = $arrPostdata['rate_type_'.$i]; 
                $edate          = ($arrPostdata['effective_date_'.$i] != '') ? $arrPostdata['effective_date_'.$i] : '00/00/0000';
                $effective_date = $this->getDatabaseDate($edate); 
                $till_date      = isset($arrPostdata['till_date_'.$i]) ? $this->getDatabaseDate($arrPostdata['till_date_'.$i]) : '0000-00-00';
                $duration       = $arrPostdata['rule_duration_'.$i];
                echo $tax            = $arrPostdata['tax_'.$i]; exit;
                $ledger_id      = $arrPostdata['et_ledger_id_'.$i];
                $lateCharge      = (isset($arrPostdata['late_charge_'.$i]))? 1 : 0;
                $conditions     = "particular = ?1 and  soc_id = ?2 AND effective_date = ?3 AND duration = ?4";
                $parameters     = array("1" => $particular, "2" => $auth['soc_id'],"3" => $effective_date, "4" => $duration);
                
                if(empty($arrPostdata['particular_id_'.$i]))
                {
                    $objIncomebilling = new IncomeCommonBilling();
//                    $objIncomebilling->created_date = $this->getCurrentDate('database')." ".date("H:i:s");
                } else {
                    $objIncomebilling = IncomeCommonBilling::findfirst('soc_id = ' . $auth['soc_id'] . ' AND id = ' .  $arrPostdata['particular_id_'.$i]);
                    
//                    if($objIncomebilling->effective_date < $today && 
//                            ($objIncomebilling->rate != $rent || $ledger_id != $objIncomebilling->ledger_id)) {
//                        
//                        $objIncomebilling->id = ''; 
//                        if(IncomeCommonBilling::count('soc_id = ' . $auth['soc_id'] . ' AND particular = "' . $particular . '" AND effective_date = "' .  $effective_date . '"')) {
//                            array_push($error_particular, $particular);
//                            $arrfinal_array['flag'] = 'fail';
//                            $arrfinal_array['err_type'] = 'duplicate_rule';
//                             
//                            $arrfinal_array['errorMessage'] = $error_particular;
//                            $err = true;
//                        }
//                    } else {
//                        
//                    }
                } 
                if (( $objIncomebilling->rate           != $rent 
                   || $ledger_id                        != $objIncomebilling->ledger_id 
                   || $objIncomebilling->effective_date != $effective_date
                   || $objIncomebilling->tax_class_id   != $tax
                   || $objIncomebilling->particular     != $particular 
                || $objIncomebilling->apply_late_payment_interest != $lateCharge)) { 
                    if($effective_date > $today && !$err) { 
//                        echo $particular . ' date is greater <br>';
                        $objIncomebilling->particular         = $particular; 
                        $objIncomebilling->rate_type          = $rent_type; 
                        $objIncomebilling->rate               = $rent; 
                        $objIncomebilling->duration           = $duration; 
                        $objIncomebilling->add_to_maintenance = 0; 
                        $objIncomebilling->ledger_id          = $ledger_id;
                        $objIncomebilling->effective_date     = $effective_date; 
                        $objIncomebilling->till_date          = $till_date; 
                        $objIncomebilling->tax_class_id       = $tax;
                        $objIncomebilling->apply_late_payment_interest = $lateCharge;
//                        $objIncomebilling->created_by         = isset($objIncomebilling->created_by)? $objIncomebilling->created_by: $auth['user_id'] ;  
                        $objIncomebilling->updated_date       = $objIncomebilling->created_date = $this->getCurrentDate('database')." ".date("H:i:s");
                        $objIncomebilling->updated_by         = $auth['user_id']; 
                        $objIncomebilling->soc_id             = $auth['soc_id']; 
                        if(!$objIncomebilling->Save())
                        {
                            $arrfinal_array['flag'] = 'fail';
                            $arrfinal_array['errorMessage'] = $objIncomebilling->getMessages();
        //                    print_r($objIncomebilling->getMessages());exit();
                            return $arrfinal_array;
                        } else {
                            $arrfinal_array['id'] = $objIncomebilling->id;
                        }
                    } else {
                        array_push($error_particular, $particular);
    //                    echo $particular . ' date is smaller <br>';
//                        $arrfinal_array['flag']     = 'fail';
//                        $arrfinal_array['err_type'] = 'past_date';
//
//                        $arrfinal_array['errorMessage'] = $error_particular;
                    }
                }
            }
        }
        /*for($i = 1 ; $i <= $arrPostdata['totalothercommonrental']; $i++)
        {
            if($arrPostdata['otherUnittype_'.$i] != '')
            {
                
                $particular = $arrPostdata['otherUnittype_'.$i];
                $rent = $arrPostdata['otherrent_'.$i];
                $effective_date = $arrPostdata['other_effective_date_'.$i];
                $duration = $arrPostdata['otherduration_'.$i];
                $otheraddmaintainance = $arrPostdata['otheraddmaintainance_'.$i];
                $conditions = "particular = ?1 and  soc_id = ?2 AND effective_date = ?3 AND duration = ?4";

                $parameters = array("1" => $particular, "2" => $auth['soc_id'],"3" => $this->getDatabaseDate($effective_date), "4" => $duration);
                $objIncomebilling = IncomeCommonBilling::findfirst(array(
                                $conditions,
                                "bind" => $parameters
                ));
                if(empty($objIncomebilling))
                {
                    $objIncomebilling = new IncomeCommonBilling();
                    $objIncomebilling->created_date = $objIncomebilling->created_date = $this->getCurrentDate('database')." ".date("H:i:s");
                }else{
                    $objIncomebilling->id = $objIncomebilling->id;
                }

                $objIncomebilling->particular = $particular; 
                $objIncomebilling->rate = $rent; 
                $objIncomebilling->duration = $duration; 
                $objIncomebilling->add_to_maintenance = isset($otheraddmaintainance) ?'1' :'0'; 
                $objIncomebilling->effective_date = $this->getDatabaseDate($effective_date); 
                
                $objIncomebilling->created_by = isset($objIncomebilling->created_date)? $objIncomebilling->created_date : $auth['user_id'] ;  
                $objIncomebilling->updated_date = $objIncomebilling->created_date = $this->getCurrentDate('database')." ".date("H:i:s");
                $objIncomebilling->updated_by = $auth['user_id']; 
                $objIncomebilling->soc_id = $auth['soc_id']; 
                
                if(!$objIncomebilling->Save())
                {
                    $arrfinal_array['flag'] = 'fail';
                    $arrfinal_array['errorMessage'] = $objIncomebilling->getMessages();
                    print_r($objIncomebilling->getMessages());exit();
                    return $arrfinal_array;
                }
            }else{
                continue;
            }
            
        }*/
        
        return $arrfinal_array;
    }
    
    /**
     * get all parking unit types
     * @method getParkingUnitType
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function getParkingUnitType($event, $component, $data=array())
    {
        $soc_id = $data['auth']['soc_id'];
        $conditions = "soc_id = ?1 AND status = ?2 AND soc_unit_type = ?3";
        $parameters = array("1" => $soc_id, "2" => '1', "3" => 'Parking');
        
        //print_r($conditions);
        $objallsocietyunits = SocUnitsTpl::find(array(
            $conditions,
            "bind" => $parameters
        ));
        
        $arrsocietyunit = array();
        if(!empty($objallsocietyunits)){
            $arrallsocietyunits = $objallsocietyunits->toArray();
        }
        return $arrallsocietyunits;
    }
    
     /**
     * Get income invoice rule
     * @method getIncomeInvoiceRule
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function getIncomeInvoiceRule($event, $component, $data = array())
    {
        $unit_type_id = implode('","', array_keys($data['arrallunittypes']));
        $income_account = implode('","', $data['incomeaccount']);
        $queryBuilder = $this->di->getModelsManager()
           ->createBuilder()
           ->columns(['rule.id', 'rule.soc_id', 'rule.soc_units_type_id', 'rule.unit_type', 'rule.income_account_id', 'rule.apply_late_payment_interest', 'rule.fee_carpet_area', 'rule.fee_open_area', 'rule.fee_per_unit', 'rule.fee_noc_tenant', 'rule.fee_noc_vacant', 'rule.other_income_fee', 'rule.other_income_fee_rate', 'rule.effective_date'])
           ->addFrom('\ChsOne\Models\Incomeinvoicerule', 'rule')
           ->where(('rule.soc_id = :soc_id:'), array('soc_id' => $data['auth']['soc_id']))
           ->andWhere(('rule.rule = :rule:'), array('rule' => 'standard'))
           ->andWhere(('rule.effective_date <= :effective_date:'), array('effective_date' => $this->getCurrentDate('database')))
           ->andWhere(('rule.soc_units_type_id IN ("'.$unit_type_id.'")'), array())
           ->andWhere(('rule.income_account_id IN ("'.$income_account.'")'), array())
           ->orderby('rule.effective_date asc, rule.soc_units_type_id asc');
        $resultset = $queryBuilder->getQuery()->execute();
        
        return $resultset->toArray();
    }
    
    /**
     * format invoice rule's post data
     * @method formatIncomeInvoiceRule
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function formatIncomeInvoiceRule($event, $component, $data = array())
    {
        $arrIncomeInvoiceRules = array();
        if(!empty($data))
        {
            foreach($data['unitTypes'] as $key=>$value)
            {
                foreach($data['incomeRules'] as $eachIncomeRule)
                {
                    if($key == $eachIncomeRule['soc_units_type_id'])
                    {
                        //if(count($arrIncomeInvoiceRules[$key])<2)
                        {
                            if(array_key_exists($eachIncomeRule['income_account_id'], $data['incomeAccount']))
                            {
                                if($data['incomeAccount'][$eachIncomeRule['income_account_id']] == 'maintenancefee')
                                {
                                    $arrIncomeInvoiceRules[$key]['maintenence_fee'] = $eachIncomeRule;
                                }
                                elseif($data['incomeAccount'][$eachIncomeRule['income_account_id']] == 'sinkingfund')
                                {
                                    $arrIncomeInvoiceRules[$key]['sinking_fund'] = $eachIncomeRule;
                                }
                            }
                        }
                    }
                }
            }
        }
        return $arrIncomeInvoiceRules;
    }
    
     /**
     * Get parking rule
     * @method getIncomeInvoiceParkingRule
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function getIncomeInvoiceParkingRule($event, $component, $data = array())
    {
        $queryBuilder = $this->di->getModelsManager()
           ->createBuilder()
           ->columns(['rule.id'])
           ->addFrom('\ChsOne\Models\Incomeinvoicerule', 'rule')
           ->where(('rule.soc_id = :soc_id:'), array('soc_id' => $data['auth']['soc_id']))
           ->andWhere(('rule.rule = :rule:'), array('rule' => 'parking'))
           ->andWhere(('rule.effective_date <= :effective_date:'), array('effective_date' => $this->getCurrentDate('database')))
           ->orderby('rule.effective_date desc')
           ->limit(1);
        $resultset = $queryBuilder->getQuery()->execute();
        return $resultset->toArray();
    }
    
    /**
     * Get late charges rule
     * @method getLateChargesRule
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return array
     */
    public function getLateChargesRule($event, $component, $data = array())
    {
        $queryBuilder = $this->di->getModelsManager()
           ->createBuilder()
           ->columns(['rule.id','rule.simple_interest','rule.grace_period','rule.calculate_from','rule.interest_amount_type','rule.interest_type','rule.effective_date', 'rule.applicable_taxes'
           ])
           ->addFrom('\ChsOne\Models\LatePaymentCharges', 'rule')
           ->where(('rule.soc_id = :soc_id:'), array('soc_id' => $data['auth']['soc_id']))
           ->andWhere(('rule.type = :type:'), array('type' => 'incidental'))
           ->andWhere(('rule.effective_date <= :effective_date:'), array('effective_date' => $this->getCurrentDate('database')))
           ->orderby('rule.effective_date desc')
           ->limit(1);
        $resultset = $queryBuilder->getQuery()->execute();
        return $resultset->toArray();
    }
    
    /**
     * Set record deposit post for ledger entries 
     * @method updateWizardSetup
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return integer
     */
    public function updateWizardSetup($event, $component, $data = array())
    {
        $objWizardSetup = new \ChsOne\Models\ChsoneWizardSetup();
        $updateResponse = $objWizardSetup->updateChsoneWizardSetupStatus(array('soc_id'=>$data['auth']['soc_id'], 'status'=>$data['status'], 'url'=>$data['pageUrl'], 'sub_url'=>$data['sub_url'],'updated_date'=> date('Y-m-d H:i:s')));
        if(!empty($updateResponse) && strtolower($updateResponse['status']) == 'success')
        {
            return 1;
        }
        return 0;
    }
    
    /**
     * Set record deposit post for ledger entries 
     * @method updateWizardSetup
     * @access public
     * @param object $event
     * @param object $component
     * @param array $data
     * @return integer
     */
    public function IsSocietySetupCompleted($event, $component, $data = array())
    {
        $arrWizardSetup = array();
        $objWizardSetup =  \ChsOne\Models\ChsoneWizardSetup::findFirst("soc_id= " . $data['auth']['soc_id']);
        if(!empty($objWizardSetup))
        {
            $arrWizardSetup = $objWizardSetup->toArray();
        }
        return $arrWizardSetup;
    }
    
        /**
     * Save invoice payment data
     * 
     * @method saveInvoicePaymentDetail
     * @access public
     * @param object $event        	
     * @param object $component        	
     * @param array $data        	
     * @return array
     */
    public function saveCommonBillCharges($event, $component, $data = array()) {
        $arrResponse = array(
            'status' => 'error'
        );
        if (isset($data ['arrParticularDetail']) && !empty($data ['arrParticularDetail'])) {
            if($data ['arrParticularDetail']['id'])
            {
                $objIncomebilling = \ChsOne\Models\IncomeCommonBilling::findFirst(array("conditions" => 'soc_id = "' . $data['auth']['soc_id']. '" AND id = ' . $data ['arrParticularDetail']['id'] ));
	    }
            else
            {
                $objIncomebilling = new IncomeCommonBilling();
                $objIncomebilling->created_date = $this->getCurrentDate('database')." ".date("H:i:s");
                $objIncomebilling->created_by         = $data['auth']['user_id'] ;  
            }
            	
            $objIncomebilling->particular         = $data ['arrParticularDetail']['particular']; 
            $objIncomebilling->rate_type          = $data ['arrParticularDetail']['rent_type']; 
            $objIncomebilling->rate               = $data ['arrParticularDetail']['rent']; 
            $objIncomebilling->duration           = $data ['arrParticularDetail']['duration']; 
            $objIncomebilling->add_to_maintenance = 0; 
            $objIncomebilling->ledger_id          = $data ['arrParticularDetail']['ledger_id'];
            $objIncomebilling->effective_date     = $data ['arrParticularDetail']['effective_date']; 
            $objIncomebilling->till_date          = $data ['arrParticularDetail']['till_date']; 
            $objIncomebilling->tax_class_id       = $data ['arrParticularDetail']['tax'];
            $objIncomebilling->apply_late_payment_interest = $data ['arrParticularDetail']['lateCharge'];
            $objIncomebilling->updated_date       = $this->getCurrentDate('database')." ".date("H:i:s");
            $objIncomebilling->updated_by         = $data['auth']['user_id']; 
            $objIncomebilling->soc_id             = $data['auth']['soc_id']; 	
            
            if (!$objIncomebilling->save()) {
                foreach ($objIncomebilling->getMessages() as $messages) {
                    $arrMessages [] = (string) $messages;
                }
                $arrResponse ['message'] = $arrMessages;
            } else {
                $arrResponse = array(
                    'status' => 'success',
                    'id' => $objIncomebilling->id
                );
            }
        }
        return $arrResponse;
    }

    public function getLastCommanCharges($event, $component, $data = array())
    {
        
        $queryBuilder = $this->di->getModelsManager()
           ->createBuilder()
           ->columns(['ch.from_date'])
           ->addFrom('\ChsOne\Models\IncomeCommonBilling', 'ch')
           ->where(('ch.soc_id = :soc_id:'), array('soc_id' => $data['soc_id']))
           ->andWhere(('ch.fk_unit_id = :unit_id:'), array('unit_id' => $data['unit_id']))
           ->andWhere(('ch.billing_type <= :billing_type:'), array('billing_type' => $data['unit_id']))
           ->orderby('ch.from_date desc')
           ->limit(1);
        $resultset = $queryBuilder->getQuery()->execute();
        return $resultset->toArray();
    }

    
}
