<?php

/**
 * This class file is a controller for add genereal society setting. PHP versions 5.5.9 Project name CHSONE
 * 
 * @version 2: app/incometracker/controllers/SettingController.php 2016-02-03 $
 * @copyright Copyright (C) 2016 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2016 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category SettingController controller
 * <AUTHOR> <Futurescape Technologies>
 * @since File available since Release 1.0
 */
/**
 *
 * @package ChsOne\incometracker\Controllers
 */

namespace ChsOne\IncomeTracker\Controllers;

use \Phalcon\Logger\Adapter\File as FileAdapter;
use ChsOne\SocietySetup\Controllers\ControllerBase;
use ChsOne\IncomeTracker\Forms\GeneralSettingForm;
use ChsOne\IncomeTracker\Forms\IncomeAccountForm;
use ChsOne\IncomeTracker\Forms\AddNonMemberMasterForm;
use ChsOne\TaxCategories\Components\Tax;
use ChsOne\Components\IncomeTracker\IncometrackerEvent;
use ChsOne\Components\Members\MemberEvent;
use Phalcon\Mvc\Model\Criteria,
    Phalcon\Config;
use ChsOne\Components\Auth;
use ChsOne\Dialtoverify,
    ChsOne\Models\Member;
use ChsOne\IncomeTracker\Forms\CommonBillingForm,
    ChsOne\IncomeTracker\Forms\AddoutstandingForm,
    ChsOne\IncomeTracker\Forms\UpdatePaymentTrackerForm,
    ChsOne\IncomeTracker\Forms\QuickBillPayForm,
    ChsOne\IncomeTracker\Forms\GenerateManualBillForm;
use ChsOne\Components\Email\Email;
use Phalcon\Mvc\View;
use ChsOne\Components\Messages\Fieldmessages;
use ChsOne\Components\Accounts\AccountsettingEvent,
    ChsOne\Components\Export\ExportEvent;
use ChsOne\Models\Units,
    ChsOne\Models\IncomeInvoiceAdjustment,
    ChsOne\Models\IncomeInvoicePayment,
    ChsOne\Models\IncomeInvoiceParticular;
use ChsOne\Models\TaxClass;
use Phalcon\Paginator\Adapter\NativeArray as Paginator,
    Phalcon\Paginator\Adapter\QueryBuilder as Paginatorbyquery,
    Phalcon\Paginator\Adapter\NativeArray as PaginatorArray,
    ChsOne\Components\Accounts\GroupLedg;
use ChsOne\Components\Facility\FacilityManagementEvent;
use Phalcon\Paginator\Adapter\NativeArray as NativeArray;

/**
 * IncomedetailsController used to register society.
 */
class IncomedetailsController extends ControllerBase {

    /**
     * before execute any action this function is executed
     * 
     * @method beforeExecuteRoute
     * @access public
     * @global type $di
     * @global type $config
     * @global array $constants
     * @param \Phalcon\Mvc\Dispatcher $dispatcher        	
     * @return boolean
     */
    public function beforeExecuteRoute(\Phalcon\Mvc\Dispatcher $dispatcher) {
        global $di, $config, $constants;
        $this->config = $config;
        $this->di = $di;

        $this->constants = $constants;

        parent::beforeExecuteRoute($dispatcher);

        $auth = $this->session->get("auth");
        if ($auth) {

            //            $this->incometrackerevent = new IncometrackerEvent($this->config);
            //
			//            $this->incometrackerevent->addListener('InvoiceGenerator', '\ChsOne\Components\IncomeTracker\Invoicing\Listeners\AutoInvoicingListener');

            $soc_id = $auth['soc_id'];
            $accountsetupflag = 1;
            //            $arrCheckEntries = array('auth' => $auth, 'db_write_connection' => $this->soc_db_w, 'accountsetupflag' => $accountsetupflag, "config" => $this->config);
            //            $arrIncomeTrackerDetails = $this->incometrackerevent->incometracker('InvoiceGenerator:accountSetUpDetails', $arrCheckEntries);
            //            ////print_r($arrIncomeTrackerDetails);exit();
            //            if (!$arrIncomeTrackerDetails['success']) {
            //                $this->response->redirect($this->config->system->full_base_url . "income-tracker-invoice-setting/incometrackersetting");
            //                return false;
            //            }
            return true;
        }
        //        else {
        //            header("location:" . $this->config->system->session_lost_url);
        //            return false;
        //        }
    }

    /**
     * Initialize parameter
     * 
     * @method initialize
     * @access public
     */
    public function initialize() {

        global $config, $constants;
        $this->contsants = $contsants;
        //$this->tag->setTitle('Income Tracker');
        $this->view->setLayout("admin");
        $this->auth = $this->session->get("auth");
        $this->incometrackerevent = new IncometrackerEvent($config);
        $this->accountsettingevent = new AccountsettingEvent($config);
        $this->accountsettingevent->addListener('Accountsetting', '\ChsOne\Components\Accounts\Listeners\AccountSettingpageListener');
        $this->incometrackerevent->addListener('accountListener', 'ChsOne\Components\IncomeTracker\Tracking\Listeners\InvoiceAccountListener');
        //$this->incometrackerevent->addListener('GenerateInvoice', '\ChsOne\Components\IncomeTracker\Invoicegenerate\Listeners\InvoiceGeneratorListener');
        $this->incometrackerevent->addListener('InvoiceGenerator', 'ChsOne\Components\IncomeTracker\Invoicegenerate\Listeners\InvoiceGeneratorListener');

        $this->incometrackerevent->addListener('MemberIncomeDetail', 'ChsOne\Components\IncomeTracker\Tracking\Listeners\MemberIncomeListener');
        $this->incometrackerevent->addListener('AutoInvoice', '\ChsOne\Components\IncomeTracker\Invoicing\Listeners\AutoInvoicingListener');
        $this->incometrackerevent->addListener('nomemberincomelistner', '\ChsOne\Components\IncomeTracker\Tracking\Listeners\NonMemberIncomeListener');
        $this->incometrackerevent->addListener('IncomeAccount', '\ChsOne\Components\IncomeTracker\Setting\Listeners\IncomeAccountListener');
        //Export Invoice
        $this->invoiceexport = new \ChsOne\Components\Export\ExportEvent($config);
        $this->invoiceexport->addlistener('InvoiceExport', '\ChsOne\Components\Export\Listeners\InvoiceExportListener');
        $this->invoiceexport->addlistener('ReportExport', '\ChsOne\Components\Export\Listeners\ReportExportListener');
        $this->incometrackerevent->addListener('commonBilling', 'ChsOne\Components\IncomeTracker\Tracking\Listeners\CommonBillingListener');

        $this->facilitymanagementevent = new FacilityManagementEvent($config);

        $this->strLogFilePath = "Invoicegenerator_" . $this->getCurrentDate('database') . ".log";
        parent::initialize();
    }

    /**
     * index page for income tracker.
     * 
     * @method indexAction
     * @access public
     */
    public function indexAction() {
        $this->response->redirect('admin/income-details/');
    }

    public function viewallInvoiceAction() {
        global $config;
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setLayout("admindashboard-new");
        $this->view->setVar("config", $config);
    }

    /**
     * member income account.
     * 
     * @method incomememberAction
     * @access public
     * @uses \ChsOne\Models\Units Units Model
     */
    public function incomememberAction() {
//        echo '<pre>';
        $options = array();
        $arrData = $this->session->get("auth");
        
        $arrAllMemberIncome = array();
        
        $this->setActionUrl('income-details/incomemember', 'income-details/incomemember');
        $this->setSearchView('income-details/incomemember', ["units.unit_flat_number" => "unit_number", "units.soc_building_name"=>"building", "units.building_unit_number" => "building_unit_number", "member_name" => "member_name"]);
        
        
        if($this->request->isPost()){
            $searchArr  =   $this->request->getPost();
            $searchMemberFilterPost['search_by'] = $searchArr['search_by'];
            $searchMemberFilterPost['search_key'] = $searchArr['search_key'];
            $this->setPostSearchFilterArr('income-details/incomemember', $searchMemberFilterPost);
            
        }
        $arrData['searchMaintenanceQuery']  = \ChsOne\Helper\SearchHelper::getSearchQuery('income-details/incomemember');
        
        $search_filter  =   $this->session->get('search_filter');
        $number_page = $this->request->getQuery("page", "int");
        $number_page = (isset($number_page) && ($number_page != '' || $number_page > 0)) ? $number_page : $search_filter['income-details/incomemember']['current_page'];
        $arrSearchData = array();
        $units = Units::find(['conditions' => 'soc_id = "' . $this->auth['soc_id'] . '" AND status = 1', 'columns' => 'unit_id,soc_building_name,unit_flat_number']);
        $members = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPrimaryMember', $this->auth);

        ////print_r($units);exit;
        $this->view->setVar('units', $units);
        $this->view->setVar('members', $members);
//        if (!empty($this->request->getPost())) {
//            $this->view->setVar('search_arr', $this->request->getPost());
//            $arrData['searchData'] = $this->request->getPost();
//        }
        $units = new Units();
        $objQueryBuiler = $units->getUnitPrimaryMemberDetails($arrData);
        
        $paginator = new Paginatorbyquery(array("builder" => $objQueryBuiler, "limit" => PAGE_NUMBER, "page" => $number_page));
        //exit;
        $page = $paginator->getPaginate();
        foreach ($page->items as $key => $value) {
            if (!isset($arrAllMemberIncome[$value->unit_id]) && empty($arrAllMemberIncome[$value->unit_id])) {
                $singleMemberDetail['soc_id'] = $value->soc_id;
                $singleMemberDetail['unit_id'] = $value->unit_id;
                $singleMemberDetail['chargeable'] = $value->chargeable;
                $is_generatedOutstanding = $this->_isGeneratedOutstanding($value->unit_id);
                //                //print_r($is_generatedOutstanding);exit();
                $singleMemberDetail['is_generatedOutstanding'] = ($is_generatedOutstanding['count'] > 0) ? 1 : 0;

                $singleMemberDetail['is_generatedBill'] = ($is_generatedOutstanding['is_already_generated'] == 0) ? 1 : 0;
                //                echo '<pre>'; //print_r($singleMemberDetail1);
                $singleMemberDetail['unit_flat_number'] = $value->unit_flat_number;
                $singleMemberDetail['unit_category'] = $value->unit_category;
                $singleMemberDetail['soc_building_floor'] = $value->soc_building_floor;
                $singleMemberDetail['soc_building_id'] = $value->soc_building_id;
                $singleMemberDetail['soc_building_name'] = $value->soc_building_name;
                $singleMemberDetail['unit_type'] = $value->unit_type;
                $singleMemberDetail['id'] = $value->id;
                $singleMemberDetail['member_first_name'] = $value->member_first_name;
                $singleMemberDetail['member_last_name'] = $value->member_last_name;
                $singleMemberDetail['member_mobile_number'] = $value->member_mobile_number;
                //Credit amount
                if (!empty($is_generatedOutstanding['is_generated'])) {
                    $singleMemberDetail['credit_detail'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getCreditAmountForIndividuals', array('data' => array('soc_id' => $value->soc_id, 'account_id' => $value->unit_id, 'account_context' => 'unit', 'bill_date' => $this->getCurrentDate('database'), 'bill_type' => 'maintenance'))); //get advance details
                } else {
                    $singleMemberDetail['credit_detail'] = array('credit_amount' => 0, 'debit_amount' => 0, 'remaining_amount' => $is_generatedOutstanding['advance_amount']);
                    $singleMemberDetail['outstanding_amount'] = $is_generatedOutstanding['outstanding_amount'];
                }
                $singleMemberDetail['invoice_detail'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceDetail', $singleMemberDetail); //get all Unit details
                $singleMemberDetail['last_invoice_date'] = (!empty($singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['invoice_date'])) ? $singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['invoice_date'] : '-';
                $arrAllMemberIncome[$value->unit_id] = $singleMemberDetail;
            }
        }
//        echo '<pre>'; print_r($arrAllMemberIncome);exit;
        //        exit();
        $this->view->setVar("arrMemberIncomeDetail", $arrAllMemberIncome);
        $this->view->setVar("page", $page);
        $this->view->setVar("form", $this->form);
        $this->view->setLayout("admin");
        $this->view->setVar("config", $this->config);
        ////print_r($this->session->get("err_msgs"));exit;
    }

    /**
     * member income account.
     * 
     * @method incomememberAction
     * @access public
     * @uses \ChsOne\Models\Units Units Model
     */
    public function getMaintenanceDuesAction($page = 1, $type = 'pdf', $soc_id = '', $genReport = 0) {
        //echo 'rajesh||'.$page.'||'.$genReport;exit;
        if ($type == 'pdf' && $genReport == 0) {
            $arrListenerdata['page'] = $page;
            $arrData = $this->session->get("auth");
            $arrListenerdata['soc_id'] = $arrData['soc_id'];
            $result = $this->invoiceexport->exportDocument('InvoiceExport:maintenanceDuesReportPdf', $arrListenerdata);
        }
        if (!empty($page)) {
            $options = array();
            if (empty($soc_id)) {
                $arrData = $this->session->get("auth");
            } else {
                $arrData['soc_id'] = $soc_id;
                $conce = $this->calMultiDbFlow($arrData['soc_id']);
                $this->di->setShared('dbSoc', $conce);
            }
            $arrAllMemberIncome = array();
            $number_page = $page; //$this->request->getQuery("page", "int");
            $number_page = ($number_page > 0) ? $number_page : 1;
            $units = new Units();
            $arrUnitDetails = $units->getUnitPrimaryMemberDetails($arrData, true);
            //            /print_r($arrUnitDetails);exit;
            foreach ($arrUnitDetails as $key => $value) {
                if (!isset($arrAllMemberIncome[$value['unit_id']]) && empty($arrAllMemberIncome[$value['unit_id']])) {
                    $singleMemberDetail['soc_id'] = $value['soc_id'];
                    $singleMemberDetail['unit_id'] = $value['unit_id'];
                    $singleMemberDetail['chargeable'] = $value['chargeable'];
                    $singleMemberDetail['unit_flat_number'] = $value['unit_flat_number'];
                    $singleMemberDetail['unit_category'] = $value['unit_category'];
                    $singleMemberDetail['soc_building_floor'] = $value['soc_building_floor'];
                    $singleMemberDetail['soc_building_id'] = $value['soc_building_id'];
                    $singleMemberDetail['soc_building_name'] = $value['soc_building_name'];
                    $singleMemberDetail['unit_type'] = $value['unit_type'];
                    $singleMemberDetail['id'] = $value['id'];
                    $singleMemberDetail['member_first_name'] = $value['member_first_name'];
                    $singleMemberDetail['member_last_name'] = $value['member_last_name'];
                    $singleMemberDetail['member_mobile_number'] = $value['member_mobile_number'];
                    $singleMemberDetail['credit_detail'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getCreditAmountForIndividuals', array('data'=>array('soc_id'=>$value['soc_id'], 'account_id'=>$value['unit_id'], 'account_context'=>'unit', 'bill_date'=>$this->getCurrentDate('database'), 'bill_type'=>'maintenance'))); //get advance details
                    $singleMemberDetail['invoice_detail'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceDetail', $singleMemberDetail); //get all Unit details
                    $arrAllMemberIncome[$value['unit_id']] = $singleMemberDetail;
                }
            }

            $arrSocietyData = array('soc_id' => $arrData['soc_id'], 'status' => 1);
            $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details

            $currentDisplayDate = $this->getCurrentDate('display');
        }

        if ($type == 'excel') {
            $arrListenerdata['format'] = 'excel';
            $arrListenerdata['auth'] = $this->session->get("auth");
            $arrListenerdata['arrMemberDueDetail'] = $arrAllMemberIncome;
            $arrListenerdata['currentDisplayDate'] = $currentDisplayDate;
            $arrListenerdata['arrSocietyDetail'] = $arrSocietyDetail;

            $result = $this->invoiceexport->exportDocument('InvoiceExport:maintenanceDuesReportExcel', $arrListenerdata);
            $this->view->disable();
        } else {
            $this->view->setVar("arrMemberIncomeDetail", $arrAllMemberIncome);
            $this->view->setVar("arrSocietyDetail", $arrSocietyDetail);
            $this->view->setVar("currentDisplayDate", $currentDisplayDate);
            $this->view->setVar("page", $page);
            $this->view->setVar("config", $this->config);

            $this->view->pick('incomedetails/maintenanceDuesReport');
            $this->view->setVar("type", 'print');
            $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
            $this->view->setLayout("printInvoice");
        }

        ////print_r($this->session->get("err_msgs"));exit;
    }

    /**
     * member income invoice list.
     * 
     * @method memberincomepaymentlistAction
     * @access public
     */
    public function memberincomepaymentlistAction($unit_id = '') {
//        return $this->response->redirect($this->config->system->full_base_url . "income-details/memberReceiptslist/$unit_id");
        
        $auth = $this->session->get('auth');
        $arrIncomeInvoiceDetail = array();
        
        $lastpage = $this->request->getQuery("page", "int");
        if(empty($lastpage) && strstr($_SERVER['HTTP_REFERER'], '/incomemember?page='))
        {
            $lastpage = array_pop(explode('page=', $_SERVER['HTTP_REFERER']));
            $lastpage = !empty($lastpage) ? $lastpage : 1;
        }
        
        if (!empty($unit_id) && is_numeric($unit_id)) {
            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $unit_id;
            $arrDataListener['show_cancelled_invoice'] = 1;
            if (!empty($this->request->getPost())) {
                $this->view->setVar('search_arr', $this->request->getPost());
                $arrDataListener['searchData'] = $this->request->getPost();
            }
            $arrCreditDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getCreditAmountForIndividuals', array('data' => array('soc_id' => $auth['soc_id'], 'account_id' => $unit_id, 'account_context' => 'unit', 'bill_date' => $this->getCurrentDate('database'), 'bill_type' => 'maintenance'))); //get advance details
            $creditAmount = (isset($arrCreditDetail['remaining_amount']) && !empty($arrCreditDetail['remaining_amount'])) ? number_format(round($arrCreditDetail['remaining_amount'], 3), 2, '.', '') : number_format(0, 2, '.', '');
            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceDetail', $arrDataListener); //get all Unit details
            $arrAllInvoiceNumber = $this->incometrackerevent->incometracker('MemberIncomeDetail:getAllInvoiceNumber', $arrDataListener); //get all Unit details
            //echo '<pre>';//print_r($arrIncomeInvoiceDetail);exit;
            //Get receipt data
            $number_page = !empty($this->request->getQuery("page", "int")) ? $this->request->getQuery("page", "int") : 1;
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'full_list' => true);
            $arrPaymentTrackerDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerList', $arrPaymentTrackerListener); // get all Unit details
//            $paginator = new Paginatorbyquery(array(
//                "builder" => $objQueryBuiler,
//                "limit" => PAGE_NUMBER,
//                "page" => $number_page
//            ));
//            $page = $paginator->getPaginate();
//            $arrPaymentTrackerDetail = $page->items->toArray();
            $arrInvoicePaymentTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPaymentTrackerListedData', array('arrPaymentTrackerDetail' => $arrPaymentTrackerDetail)); //get all Unit details

            $this->view->setVar("arrInvoicePaymentTracker", $arrInvoicePaymentTracker);
        } else {

            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember?page=".$lastpage);
        }
//        /echo '<pre>';print_r($arrCreditDetail);exit;
        $this->view->setVar("creditAmount", $creditAmount);
        $this->view->setVar("lastPage", $lastpage);
        $this->view->setVar("arrAllInvoiceNumber", $arrAllInvoiceNumber);
        $this->view->setVar("arrIncomeInvoiceDetail", $arrIncomeInvoiceDetail['unit_invoice_detail']);
        $this->view->setVar("nonCancelledInvoice", $arrIncomeInvoiceDetail['total_non_cancelled_invoice']);

        $this->view->setVar("config", $this->config);
    }

    /**
     * non member income account.
     * 
     * @method incomenonmemberAction
     * @access public
     */
    public function incomenonmemberAction() {
        $options = array();
        $auth = $this->session->get("auth");

        $objNonmemberIncome = new \ChsOne\Models\NonmemberIncome();
        $arrData = $this->request->getPost();
        $number_page = $this->request->getQuery("page", "int");
        $number_page_app = ($number_page > 0) ? $number_page : 1;
        $objQueryBuiler = $objNonmemberIncome->getNonmemberIncomeDetails(array('soc_id' => $auth['soc_id']));
        $paginator = new Paginatorbyquery(array("builder" => $objQueryBuiler, "limit" => PAGE_NUMBER, "page" => $number_page_app));
        $page = $paginator->getPaginate();
        $arrAllMemberIncome = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberInvoiceList', array('nonmember_detail' => $page->items)); //get all payment details

        $this->view->setVar("arrNonMemberIncomeDetail", $arrAllMemberIncome);
        $this->view->setVar("page", $page);
        $this->view->setLayout("admin");
        $this->view->setVar("config", $this->config);
    }

    /**
     * non member add income account.
     * 
     * @method addnonmemberincomeAction
     * @access public
     */
    public function addnonmemberincomeAction() {
        $auth = $err_msgs = array();
        $arrPost = array();
        $arrPost = $this->request->getPost();
        $auth = $this->session->get('auth');
        $objCommonHelper = new \ChsOne\Helper\CommonHelper();
        $options = array();
        $referer_url = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : $this->config->system->full_base_url . "income-details/addnonmemberincome";
        
        $page = $this->request->getQuery("page", "int");
        if(empty($page) && strstr($_SERVER['HTTP_REFERER'], '/incomenonmember?page='))
        {
            $page = array_pop(explode('page=', $_SERVER['HTTP_REFERER']));
            $page = !empty($page) ? $page : 1;
        }
        //get non member Income accounts 
        $this->incometrackerevent->addListener('generalsetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\IncomeAccountListener');
        $arrListenerdata = array('auth' => $auth);
        $arrnonmemberbankledger = $this->incometrackerevent->incometracker('generalsetting:getnonmemberincomedetails', $arrListenerdata);
        $arrnonmemberincomeAccount = array();
        $arrnonmemberincomeAccount[''] = "Select Income Account";
        if (!empty($arrnonmemberbankledger)) {
            foreach ($arrnonmemberbankledger as $key => $value) {
                $arrnonmemberincomeAccount[$value['account_id']] = ucwords($value['account_name']);
            }
        }
        $options['arrnonmemberincomeAccount'] = $arrnonmemberincomeAccount;

        $arrSetting = $this->getGeneralSetting(); //echo '<pre>'; print_r($arrSetting); exit;
        $options['arrApplicableTaxes'] = $arrSetting;
        $this->view->setVar("arrApplicableTaxes", $arrSetting);
        $arrApplicableTaxes = array();
        $arrTaxdetails = TaxClass::find(array('conditions' => array("soc_id = " . $this->auth['soc_id'] . " AND status = 1"), 'columns' => array('tax_class_id')));
        //echo '<pre>'; print_r(); exit;
        $arrSetting['INCOME_APPLICABLE_TAXES'] = array_column($arrTaxdetails->toArray(), 'tax_class_id');
        if ($arrSetting['INCOME_APPLICABLE_TAXES'] != '') {
            $income_applicabletaxes = array_column($arrTaxdetails->toArray(), 'tax_class_id'); //explode(",", $arrSetting['INCOME_APPLICABLE_TAXES']);
            $arrtaxListenerData = array('incomeapplicabletaxes' => $income_applicabletaxes, 'effective_date' => $arrListenerdataForApplicableTaxes['effective_date'], 'auth' => $auth, "modelmanaber" => $this->modelsManager);
            $this->incometrackergeneralsettingevent->addListener('incomeaccount', '\ChsOne\Components\Tax\Listeners\TaxesListener');
            $arrApplicableTaxes = $this->incometrackergeneralsettingevent->incometracker('incomeaccount:getAllTaxesOnDate', $arrtaxListenerData);
        }
        $this->view->setVar("arrTax", $arrApplicableTaxes);
        $options['arrApplicableTaxes'] = $arrApplicableTaxes;

        $arrTaxDetails = array();
        if (!empty($arrApplicableTaxes)) {
            $this->taxclasses = new \ChsOne\Components\Tax\TaxExemptionEvent($this->config); // tax exemption event is used for fire event.
            $this->taxclasses->addListener('taxClasses', '\ChsOne\Components\Tax\Listeners\TaxesListener');

            $arrListenerdata['auth'] = $auth;
            $arrListenerdata['status'] = 1;
            $arrListenerdata['modelmanager'] = $this->modelsManager;
            $arrListenerdata['tax_ids'] = array_keys($arrApplicableTaxes);
            $arrTaxDetails = $this->taxclasses->taxExemption('taxClasses:getAllCurrentTaxClasses', $arrListenerdata);
            $arrTaxDetailsWithKeyID = array();
            foreach ($arrTaxDetails as $k => $v) {
                unset($v['tax_class_description'],$v['tax_categories_footer']);
                if(isset($arrTaxDetailsWithKeyID[$v['fk_tax_class_id']]) && !empty($arrTaxDetailsWithKeyID[$v['fk_tax_class_id']]))
                {
                    $arrTaxDetailsWithKeyID[$v['fk_tax_class_id']]['tax_categories_amount'] += $v['tax_categories_amount'];
                }
                else
                {
                    $arrTaxDetailsWithKeyID[$v['fk_tax_class_id']] = $v;
                }
            }
        }
        //echo '<pre>';print_r($arrTaxDetailsWithKeyID);exit;
        $this->view->setVar("CurrentTaxDetails", (json_encode($arrTaxDetailsWithKeyID)));

        $this->incometrackerevent->addListener('incomeGeneralSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
        $options['generalsetting'] = $this->incometrackerevent->incometracker('incomeGeneralSetting:getgeneralsetting', array('auth' => $auth));

        $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id' => $auth['soc_id'])); //get all Unit details
        $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail' => $arrAccountDetail)); //get all Unit details

        $showBankAccount = 0;
        if (!empty($arrLedgerAccountDetail['arrBank'])) {
            $options['bank_account'] = $arrLedgerAccountDetail['arrBank'];
            $options['bank_selected'] = $arrLedgerAccountDetail['bank']['ledger_id'];
            if (count($arrLedgerAccountDetail['arrBank']) > 1) {
                $showBankAccount = 1;
            }
        }

        //initialize the form
        $form = new \ChsOne\IncomeTracker\Forms\AddnonmemberIncomeForm($nonmemberincome, $options);

        // if form is posted then, process the request to validate and save records appropriately.
        if ($this->request->getPost()) {
            $PostData = $this->request->getPost();
            $PostData['tax_class_id'] = str_replace('Select tax class', '', $PostData['tax_class_id']);
            //For TDS adjustment
            if (!empty($PostData['tds_amount']) && $PostData['tds_amount'] > 0) {
                $PostData['payment_amount'] = (float) $PostData['payment_amount'] + $PostData['tds_amount'];
            }

            //Do not create bill if financial years accounting closed    
            $PostData['arrCloseAccountGeneration'] = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id' => $auth['soc_id'], 'bill_date' => $PostData['bill_date']));
            if ($PostData['payment_amount'] > 0 && empty($countLedgerEntry)) {
                $PostData['arrCloseAccountPayment'] = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id' => $auth['soc_id'], 'bill_date' => $PostData['payment_date']));
            }
            //echo '<pre>';////print_r($PostData);exit;
            if ($form->isValid($PostData) != false) {
                //get new generate invoice id 
                $arrListnerData = array();
                if(!empty($PostData['tax_class_id']))
                {
                    $PostData['tax_amount'] = (strtolower($arrTaxDetailsWithKeyID[$PostData['tax_class_id']]['tax_categories_type']) == 'fixed') ? $arrTaxDetailsWithKeyID[$PostData['tax_class_id']]['tax_categories_amount'] : $PostData['booking_charge'] * ($arrTaxDetailsWithKeyID[$PostData['tax_class_id']]['tax_categories_amount']/100);
                }
                $transaction_amount = (float) round(($PostData['booking_charge'] + $PostData['tax_amount']) - $PostData['discount_amount'], 3);
                $auth = $this->session->get('auth');
                $arrListnerData['soc_id'] = $auth['soc_id'];

//                //Do not create bill if financial years accounting closed    
//                $arrClosedAccountDetail = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id'=>$auth['soc_id'], 'bill_date'=>$PostData['bill_date']));
//                if( count($arrClosedAccountDetail)>0 ) {
//                    $this->session->set("err_msg", 'Accounting has been closed for this date.');
//                    return $this->response->redirect('admin/income-details/incomenonmember');
//                }


                $this->incometrackerevent->addListener('nonmeberIncome', 'ChsOne\Components\IncomeTracker\Invoicegenerate\Listeners\InvoiceGeneratorListener');
                $invoice_id = $this->incometrackerevent->incometracker('nonmeberIncome:generate_invoice_id', $arrListnerData);
                if ($invoice_id) {
                    //add bill data for nonmember
                    //$this->incometrackerevent->addListener('nomemberincomelistner', '\ChsOne\Components\IncomeTracker\Tracking\Listeners\NonMemberIncomeListener');
                    $arrListnerData = array();
                    $PostData['transaction_reference'] = $PostData['cheque_number'];
                    $PostData['payment_instrument'] = $PostData['payer_bank_details'];
                    $arrListnerData['PostData'] = $PostData; //$this->request->getPost();
                    $arrListnerData['auth'] = $this->session->get('auth');
                    $arrListnerData['invoice_id'] = $invoice_id;
                    //$arrNonMemberIncomedetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberIncomeDetail', $arrListnerData);
                    $dueAmountRemaining = $transaction_amount;
                    $arrListnerData['PostData']['payment_status'] = 'unpaid';
                    if ($arrListnerData['PostData']['payment_amount'] >= $transaction_amount) {
                        $arrListnerData['PostData']['payment_status'] = 'paid';
                        $dueAmountRemaining = 0;
                    } elseif ($arrListnerData['PostData']['payment_amount'] < $transaction_amount && $arrListnerData['PostData']['payment_amount'] > 0) {
                        $arrListnerData['PostData']['payment_status'] = 'partialpaid';
                        $dueAmountRemaining = (float) round($transaction_amount - $arrListnerData['PostData']['payment_amount'], 3);
                    }

                    $this->soc_db_w = $this->di->getShared('soc_db_w');
                    $this->soc_db_w->begin();
                    $arrNonmemberMaster = [];
                    //Add Nonmember Master
                    $arrNonmemberMasterResponse['success'] = true;
                    if (empty($PostData['nonmember_id'])) {

                        if (($PostData['nonmem_ledger'] == 1) && ($PostData['is_default_ledger'] == 0)) {
                            $ledgerName = $PostData['booker_name'];
                            $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $auth, 'ledger_name' => $PostData['booker_name'], 'context' => SUNDRY_DEBTORS_GROUP));
                            $isDefaultDefault = 0;
                        } else {
                            $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
                            $isDefaultDefault = 1;
                        }

                        $arrNonmemberMaster = $this->incometrackerevent->incometracker('nomemberincomelistner:getDbFormatNonmemberMaster', array('arrPostData' => $PostData, 'arrNonmemberLedgerDetails' => $arrBookerLedgerDetails));
                        $arrNonmemberMaster['is_default_ledger'] = $isDefaultDefault;
                        if (!empty($arrNonmemberMaster['first_name'])) {
                            $arrNonmemberMasterResponse = $this->incometrackerevent->incometracker('nomemberincomelistner:addNonmember', array('arrPostData' => $arrNonmemberMaster, 'auth' => $auth));
                            if ($arrNonmemberMasterResponse['success'] == true) {
                                $arrListnerData['PostData']['nonmember_id'] = $PostData['nonmember_id'] = $arrNonmemberMasterResponse['nonmember_id'];
                            }
                        }
                    } else {
                        $arrNonmemberMasterDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberDetail', array('soc_id' => $auth['soc_id'], 'nonmember_id' => $PostData['nonmember_id']));
                        if (!empty($arrNonmemberMasterDetail['nonmember_ledger_id'])) {
                            $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => array("soc_id" => $auth['soc_id']), 'ledger_id' => $arrNonmemberMasterDetail['nonmember_ledger_id']));
                        }
                    }
                    //Credit adjustment for nonmember
                    if ($dueAmountRemaining > 0) {
                        $arrCreditData['soc_id'] = $auth['soc_id'];
                        $arrCreditData['account_id'] = $PostData['nonmember_id'];
                        $arrCreditData['account_context'] = 'nonmember';
                        $arrCreditData['bill_date'] = $this->getDatabaseDate($PostData['bill_date']);
                        $creditRemainingAmount = $this->incometrackerevent->incometracker('MemberIncomeDetail:getCreditAmountForIndividuals', array('data' => $arrCreditData));

                        $arrAdjustmentDetail = $this->incometrackerevent->incometracker('commonBilling:commonBillAdvanceAdjustment', array('advanceAmount' => $creditRemainingAmount['remaining_amount'], 'dueAmount' => $dueAmountRemaining));
                        if ($arrAdjustmentDetail['adjustment_amount'] > 0) {
                            $arrListnerData['PostData']['payment_status'] = $arrAdjustmentDetail['payment_status'];
                            $arrListnerData['PostData']['credit_amount'] = $creditRemainingAmount['remaining_amount'];
                            $arrCreditData['transaction_type'] = 'dr';
                            $arrCreditData['context'] = 'system';
                            $arrCreditData['payment_date'] = (!empty($arrListnerData['PostData']['bill_date'])) ? $this->getDisplayDate($arrListnerData['PostData']['bill_date']) : $this->getCurrentDate('display');
                            $arrCreditData['narration'] = 'Amount Rs ' . $arrAdjustmentDetail['adjustment_amount'] . ' is adjusted against Invoice no ' . $invoice_id;
                            $arrCreditData['payment_amount'] = $arrAdjustmentDetail['adjustment_amount'];
                            $arrCreditData['invoice_number'] = $invoice_id;

                            $arrCreditData['account_name'] = $arrListnerData['PostData']['booker_name'];
                            $creditAccountresponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveCreditAccountResponse', array('auth' => true, 'process' => 'fetch', 'soc_id' => $auth['soc_id'], 'id' => '', 'data' => $arrCreditData, 'user' => $auth['user_id'], 'username' => $auth['user_first_name'] . ' ' . $auth['user_last_name']));
                            if ($creditAccountresponse['error']) {
                                $this->soc_db_w->rollback(); //rollback in case of failure
                                $this->session->set("err_msg", 'Unable to save debit entry for ' . $invoice_id);
                                return $this->response->redirect('admin/income-details/incomenonmember');
                            }
                        }
                    }

                    //print_r($PostData);exit;
                    //print_r($arrNonmemberMasterResponse);exit;
                    $arrListnerData['PostData']['from_date'] = $this->getDatabaseDate($PostData['from_date']);
                    $arrListnerData['PostData']['end_date'] = $this->getDatabaseDate($PostData['end_date']);
                    $arrListnerData['PostData']['bill_date'] = $this->getDatabaseDate($PostData['bill_date']);
                    $addmemberincome = $this->incometrackerevent->incometracker('nomemberincomelistner:add_non_member_income', $arrListnerData);
                    //                    echo '<pre>'; print_r($addmemberincome); exit;
                    if ($addmemberincome['success'] == true && $arrNonmemberMasterResponse['success'] == true) {
                        //get income accounts
                        $arrListnerData = array();
                        $arrListnerData['auth'] = $auth;
                        $arrListnerData['nonmemberincomeaccount'] = $PostData['nonmemberincomeaccount'];
                        $arrListnerData['PostData'] = $PostData;
                        $arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:getIncomeAccountLedgerDetails', $arrListnerData);
                        $arrListnerData['PostData']['tax_class_name'] = (!empty($PostData['tax_class_id'])) ? TaxClass::findFirst("tax_class_id = " . $PostData['tax_class_id'])->tax_class_name : '';
                        $arrListnerData['PostData']['billing_type'] = $PostData['nonmemberincomeaccount'];
                        $arrListnerData['PostData']['payment_amount'] = $PostData['due_amount'] - $PostData['tax_amount'];
                        $arrListnerData['PostData']['particular'] = \ChsOne\Models\IncomeAccounts::findFirst("account_id = " . $PostData['nonmemberincomeaccount'])->account_name;
                        $arrListnerData['PostData']['invoice_number'] = $invoice_id;
                        //print_r($arrListnerData['PostData']);exit;
                        if ($arrListnerData['PostData']['tax_class_id']) {
                            $taxEntry = $this->incometrackerevent->incometracker('commonBilling:addTaxLog', array('auth' => $this->auth, 'arrPostData' => $arrListnerData['PostData'], 'arrUnitDetails' => $arrUnitDetails));

                            if (isset($taxEntry['status']) && strtolower($taxEntry['status']) == 'success') {
                                $arrListnerData['PostData']['taxLogDetail'] = $taxEntry['tax_log_detail'];
                            }
                        }
                        //Create ledger before of non member
                        //check booker ledger is exist or not
                        if (empty($arrBookerLedgerDetails['recieving_ledger_id'])) {

                            if (($PostData['nonmem_ledger'] == 1) && ($PostData['is_default_ledger'] == 0)) {
                                $ledgerName = $PostData['booker_name'];
                                $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $auth, 'ledger_name' => $PostData['booker_name'], 'context' => SUNDRY_DEBTORS_GROUP));
                            } else {
                                $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
                            }

                            //$arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
                        }
                        //echo '<pre>';print_r($PostData);exit;
                        //get bill entry 
                        $arrListener_data['soc_id'] = $auth['soc_id'];
                        $arrListener_data['invoice_number'] = $invoice_id;
                        $arrListener_data['unit_id'] = 0;
                        $arrUnitDetails['soc_building_name'] = '';
                        $arrUnitDetails['unit_flat_number'] = '';
                        $arrUnitDetails['ledger_account_id'] = 0;
                        $arrUnitDetails['soc_building_id'] = 0;
                        $arrUnitDetails['soc_building_name'] = 0;
                        $arrUnitDetails['soc_building_floor'] = 0;
                        $arrListener_data['arrUnitData'] = $arrUnitData;
                        $arrListener_data['start_date'] = $this->getDatabaseDate($PostData['from_date']);
                        $arrListener_data['end_date'] = $this->getDatabaseDate($PostData['end_date']);
                        $arrListener_data['bill_date'] = $PostData['bill_date'];
                        $arrListener_data['totalInvoceAmount'] = $transaction_amount;
                        $arrListener_data['trial'] = "live";
                        $arrInvoice_data = $this->incometrackerevent->incometracker('nonmeberIncome:saveUnitwiseInvoices', $arrListener_data);
                        $arrListnerData['PostData']['narration1'] = "$PostData[narration] against $addmemberincome[invoice_number] dated " . $this->getDisplayDate($PostData['bill_date']) . " for period ".$this->getDisplayDate($PostData['from_date'])." to ".$this->getDisplayDate($PostData['end_date']);;
                        $arrListnerData['PostData']['bill_amount'] = (float) round($transaction_amount - $arrListnerData['PostData']['tax_amount'], 2);
                        $countLedgerEntry = $this->incometrackerevent->incometracker('nomemberincomelistner:addNonmeberBillLedger', array('auth' => $auth, 'arrPostData' => $arrListnerData['PostData'], 'arrBookerLedgerDetails' => $arrBookerLedgerDetails, 'arrIncomeAccounts' => $arrIncomeAccounts));

                        //If User is paying advance amount
                        if ($PostData['payment_amount'] > 0 && empty($countLedgerEntry)) {
//                            //Do not create bill if financial years accounting closed    
//                            $arrClosedAccountDetail = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id'=>$auth['soc_id'], 'bill_date'=>$PostData['payment_date']));
//                            if( count($arrClosedAccountDetail)>0 ) {
//                                $this->soc_db_w->rollback();
//                                $this->session->set("err_msg", 'Accounting has been closed for this payment date.');
//                                return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
//                            }
                            //add advance payment entry
                            $arrListnerPaymentData = array();
                            //$arrPost = $this->request->getPost();
                            //$PostData['transaction_charges'] = $transaction_amount;
                            $PostData['payment_type'] = 'reciept';
                            $PostData['bill_number'] = $PostData['bill_number'] = $invoice_id;

                            $arrListnerPaymentData['invoice_details'] = $PostData;
                            $arrListnerPaymentData['auth'] = $this->session->get('auth');
                            $arrListnerPaymentData['nonmember_invoice_id'] = $addmemberincome['nonmember_bill_id'];

                            $arrPaymentTrackerData = $arrListnerPaymentData['invoice_details'];
                            $arrPaymentTrackerData['receipt_number'] = $this->incometrackerevent->incometracker('InvoiceGenerator:generate_receipt_id', array('soc_id' => $auth['soc_id']));
                            $arrPaymentTrackerData['transaction_reference'] = $arrListnerPaymentData['invoice_details']['cheque_number'];
                            $arrPaymentTrackerData['payment_instrument'] = $arrListnerPaymentData['invoice_details']['payer_bank_details'];
                            $arrPaymentTrackerData['bill_type'] = 'nonmember';
                            $arrPaymentTrackerData['member_paid_invoice'] = $arrListnerPaymentData['invoice_details']['bill_number'];
                            $arrPaymentTrackerData['advance_payment'] = 1;

                            $arrResponseTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveInvoicePaymentTracker', array('soc_id' => $auth['soc_id'], 'postData' => $arrPaymentTrackerData)); //get all Unit details

                            if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {
                                if (!empty($arrListnerPaymentData['invoice_details']['payment_mode']) && !in_array(strtolower($arrListnerPaymentData['invoice_details']['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                                    $addmemberincomepayment = $this->incometrackerevent->incometracker('nomemberincomelistner:addadvancepayment', $arrListnerPaymentData);
                                    if ($addmemberincomepayment['success'] == true) {
                                        $intBookerLedgerDetails = $this->incometrackerevent->incometracker('nomemberincomelistner:payNonmemberBillLedger', array('auth' => $auth, 'postData' => $PostData, 'arrBookerLedgerDetails' => $arrBookerLedgerDetails, 'arrIncomeAccounts' => $arrIncomeAccounts));
                                    }
                                } else {
                                    $this->soc_db_w->commit();
                                    if (!empty($arrResponseTracker['payment_tracker_id'])) {
                                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'P', 'transaction_status' => 'complete'));
                                    }
                                    $successmsg = "payment is succesfully added to tracker waiting for clearance.";
                                    $this->session->set("succ_msg", $successmsg);
                                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
                                }
                            }

                            if (!empty($intBookerLedgerDetails)) {
                                $this->soc_db_w->commit();
                                //Update payment status in case of cash pay
                                if (!empty($arrResponseTracker['payment_tracker_id'])) {
                                    $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'Y', 'transaction_status' => 'complete'));
                                }

                                $successmsg = "payment is added succesfully";
                                $this->session->set("succ_msg", $successmsg);
                                return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
                            } else {
                                $this->soc_db_w->rollback();
                                $err_msgs[] = "Transaction for advance payment is not done.";
                            }
                        } elseif (empty($countLedgerEntry)) {
                            $this->soc_db_w->commit();
                            $this->session->set("succ_msg", "Non member invoice created successfully");
                        } else {
                            $this->soc_db_w->rollback();
                            $err_msgs[] = "Unable to create non member invoice.";
                        }

                        if ($this->request->getPost('save', 'striptags') == "savenew") {
                            return $this->response->redirect($this->config->system->full_base_url . "income-details/addnonmemberincome");
                        }

                        return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
                    } else {
                        $err_msgs = array_merge($err_msgs, $addmemberincome['arrMessage']);
                        $this->soc_db_w->rollback();
                    }
                } else {
                    $err_msgs[] = "Please check the general setting. Invoice number is not generated.";
                }
            } else {
                $options = $this->request->getPost();
                if (!empty($bookFacilityId)) {
                    $options['facility_booking_id'] = $bookFacilityId;
                }
                //collect form error messages
                foreach ($form->getMessages() as $message) {
                    $err_msgs[$message->getField()] = (string) $message;
                } // end of foreach form getMessages
            }
        }

        $this->view->setVar("page", $page);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("constants", $this->constants);
        $this->view->setVar("bookFacilityId", $bookFacilityId);
        $this->view->setVar("form", $form);
        $this->view->setVar("options", $options);
        $this->view->setVar("err_msgs", $err_msgs);
        $this->view->setVar("referer_url", $referer_url);
        $this->view->setVar("showBankAccount", $showBankAccount);
    }

    /*
     * public function cancel_nonmember_billA ction($billnum){ $options = array(); $form = new \ChsOne\IncomeTracker\Forms\CancelBillForm($facility_booking, $options); $this->view->setVar("config", $this->config); $this->view->setVar("constants", $this->constants); $this->view->setVar("form", $form); $this->view->setVar("options", $options); $this->view->setVar("err_msgs", $err_msgs); }
     */

    public function view_nonmember_billAction($bill_number) {
        
    }

    public function pay_nonmember_billAction($bill_number) {
        //echo '<pre>';
        $auth = $this->session->get('auth');
        $arroptions = array();
        if (empty($bill_number)) {
            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
        } else {
            
            $page = $this->request->getQuery("page", "int");
            if(empty($page) && strstr($_SERVER['HTTP_REFERER'], '/incomenonmember?page='))
            {
                $page = array_pop(explode('page=', $_SERVER['HTTP_REFERER']));
                $page = !empty($page) ? $page : 1;
            }
            $arrNonMemberIncome = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberUnpaidIncomeDetail', array('soc_id' => $auth['soc_id'], 'bill_number' => $bill_number));
            if (empty($arrNonMemberIncome)) {
                $this->session->set("err_msg", 'No record found.');
                return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
            }
            //$totalPaidAmount = $this->incometrackerevent->incometracker('nomemberincomelistner:getTotalIncomePaymentDetail', array('soc_id'=>$auth['soc_id'], 'bill_number'=>$bill_number));
            if (!empty($arrNonMemberIncome)) {
                $arrPaymentDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberPaymentDetail', array('soc_id' => $auth['soc_id'], 'nonmember_bill_id' => $arrNonMemberIncome['nonmember_bill_id']));
                if (!empty($arrPaymentDetail)) {
                    $totalPaidAmount = (float) $arrPaymentDetail['payment_amount'] + $arrPaymentDetail['tds_deducted'];
                }
            }
            //echo $totalPaidAmount;exit;
            if (!empty($totalPaidAmount) && $totalPaidAmount > 0) {
                $totalPaidAmount = (float) round($totalPaidAmount - $arrNonMemberIncome['advance_amount'], 2);
            }

            $arroptions['bill_number'] = $bill_number;
            $arroptions['booker_name'] = $arrNonMemberIncome['billed_name'];
            $arroptions['bill_total_amount'] = (float) round($arrNonMemberIncome['bill_amount'], 2);
            $arroptions['discount_amount'] = (float) round($arrNonMemberIncome['discount_amount'], 2);
            $arroptions['advance_amount'] = (float) round($arrNonMemberIncome['advance_amount'] + $arrNonMemberIncome['credit_amount'], 2);
            $arroptions['partial_paid_amount'] = $totalPaidAmount;
            $arroptions['tax_deduction'] = (float) round($arrNonMemberIncome['total_taxes'], 2);
            $arroptions['due_amount'] = (float) round($arrNonMemberIncome['bill_amount'] - $arrNonMemberIncome['total_deduction'] + $arrNonMemberIncome['total_taxes'], 2);
        }
        $referer_url = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : $this->config->system->full_base_url . "income-details/addnonmemberincome";
        $this->incometrackerevent->addListener('generalSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
        $generalsettingparameter = $this->incometrackerevent->incometracker('generalSetting:getgeneralsetting', array('auth' => $auth));
        $arroptions['generalsetting'] = $generalsettingparameter;

        //get all bank and cash accounts
        $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id' => $auth['soc_id'])); //get all Unit details
        $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail' => $arrAccountDetail)); //get all Unit details

        $showBankAccount = 0;
        if (!empty($arrLedgerAccountDetail['arrBank'])) {
            $arroptions['bank_account'] = $arrLedgerAccountDetail['arrBank'];
            $arroptions['bank_selected'] = $arrLedgerAccountDetail['bank']['ledger_id'];
            if (count($arrLedgerAccountDetail['arrBank']) > 1) {
                $showBankAccount = 1;
            }
        }

        $form = new \ChsOne\IncomeTracker\Forms\PayBillForm($facility_booking, $arroptions);
        if ($this->request->isPost()) {
            $arrPostData = $this->request->getPost();
            $totalDueAmount = (float) round($arroptions['due_amount'] - $arroptions['advance_amount'] - $arroptions['partial_paid_amount'], 3);

            $arrPostData['bill_type'] = 'nonmember';
            //For TDS adjustment
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                $arrPostData['payment_amount'] = (float) $arrPostData['payment_amount'] + $arrPostData['tds_amount'];
            }
            //Getting extra paid amount of nonmember for credit note
            if ($arrPostData['payment_amount'] > $totalDueAmount) {
                $arrPostData['extraPaidAmount'] = (float) round($arrPostData['payment_amount'] - $totalDueAmount, 3);
            }
            $arrPostData['arrCloseAccountPayment'] = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id' => $soc_id, 'bill_date' => $arrPostData['payment_date']));

            if ($form->isValid($arrPostData) != false) {
//                $this->incometrackerevent->addListener('accountListener', '\ChsOne\Components\IncomeTracker\Tracking\Listeners\InvoiceAccountListener');
//                //Do not create bill if financial years accounting closed    
//                $arrClosedAccountDetail = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id'=>$soc_id, 'bill_date'=>$arrPostData['payment_date']));
//                if( count($arrClosedAccountDetail)>0 ) {
//                    $this->session->set("err_msg", 'Accounting has been closed for this payment date.');
//                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
//                }
                //check booker ledger is exist or not
                $arrListnerData = array();

                if (!empty($arrNonMemberIncome['nonmember_id'])) {
                    $arrNonmemberMasterDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberDetail', array('soc_id' => $soc_id, 'nonmember_id' => $arrNonMemberIncome['nonmember_id']));
                    if (!empty($arrNonmemberMasterDetail['nonmember_ledger_id'])) {
                        $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => array("soc_id" => $soc_id), 'ledger_id' => $arrNonmemberMasterDetail['nonmember_ledger_id']));
                    }
                } else {
                    $arrListnerData['auth'] = $auth;
                    $arrListnerData['ledger_name'] = 'Nonmember Income';
                    $arrListnerData['context'] = SUNDRY_DEBTORS_GROUP;
                    $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', $arrListnerData);
                }

                //add advance payment entry
                $arrListnerPaymentData = array();
                //$arrPostData = $this->request->getPost();
                //$transaction_amount = ($arrPostData['booking_charge'] - $arrPostData['discount_amount'] + $arrPostData['tax_amount']);
                //$arrPostData['transaction_charges'] = $transaction_amount;
                $arrPostData['payment_type'] = 'receipt';
                $arrPostData['bill_number'] = $arrPostData['member_paid_invoice'] = $bill_number;
                $arrPostData['narration'] = ""; //$arrPostData['narration'];
                $arrPostData['transaction_reference'] = $arrPostData['cheque_number'];
                $arrPostData['payment_instrument'] = $arrPostData['payer_bank_details'];

                $arrListnerPaymentData['invoice_details'] = $arrPostData;
                $arrListnerPaymentData['auth'] = $this->session->get('auth');
                $arrListnerPaymentData['nonmember_invoice_id'] = $arrNonMemberIncome['nonmember_bill_id'];

                $arrPaymentTrackerData = $arrListnerPaymentData['invoice_details'];
                $arrPaymentTrackerData['receipt_number'] = $this->incometrackerevent->incometracker('InvoiceGenerator:generate_receipt_id', array('soc_id' => $auth['soc_id']));

                $arrResponseTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveInvoicePaymentTracker', array('soc_id' => $auth['soc_id'], 'postData' => $arrPaymentTrackerData)); //get all Unit details

                $arrPaymentTrackerData['soc_id'] = $auth['soc_id'];

                $paymentToken = $this->incometrackerevent->incometracker('MemberIncomeDetail:generatePaymentToken', array('arrPaymentTracker' => $arrPaymentTrackerData)); //get all Unit details
                $isTokenValid = $this->incometrackerevent->incometracker('MemberIncomeDetail:paymentTokenVerification', array('paymentToken' => $paymentToken)); //get all Unit details

                $this->soc_db_w = $this->di->getShared('soc_db_w');
                $this->soc_db_w->begin();
                if ($isTokenValid && !empty($arrResponseTracker['payment_tracker_id'])) {
                    $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'transaction_status' => 'in_process'));
                    if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {
                        if (!empty($arrListnerPaymentData['invoice_details']['payment_mode']) && !in_array(strtolower($arrListnerPaymentData['invoice_details']['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                            $addmemberincomepayment = $this->incometrackerevent->incometracker('nomemberincomelistner:addadvancepayment', $arrListnerPaymentData);
                            if ($addmemberincomepayment['success'] == true) {
                                if ($arrPostData['payment_amount'] > 0) {
                                    $payment_status = 'paid';
                                    if ($arrPostData['payment_amount'] < $arrPostData['due_amount']) {
                                        $payment_status = 'partialpaid';
                                    }

                                    //Update only if payment status is not same
                                    if (strtolower($arrNonMemberIncome['payment_status']) != strtolower($payment_status)) {
                                        $updateIncomeStatus = $this->incometrackerevent->incometracker('nomemberincomelistner:updateNonmemberIncomeStatus', array('soc_id' => $auth['soc_id'], 'status' => $payment_status, 'bill_number' => $arrPostData['bill_number']));
                                    }
                                    //Update only in case of extra payment
                                    if (!empty($arrPostData['extraPaidAmount']) && $arrPostData['extraPaidAmount'] > 0) {
                                        $arrCreditData['soc_id'] = $auth['soc_id'];
                                        $arrCreditData['account_id'] = $arrNonMemberIncome['nonmember_id'];
                                        $arrCreditData['account_context'] = 'nonmember';
                                        $arrCreditData['transaction_type'] = 'cr';
                                        $arrCreditData['context'] = 'system';
                                        $arrCreditData['credit_used_type'] = 'adjustable';
                                        $arrCreditData['used_for'] = '';
                                        $arrCreditData['payment_date'] = !empty($arrPostData['payment_date']) ? $this->getDisplayDate($arrPostData['payment_date']) : $this->getCurrentDate('display');
                                        ;
                                        $arrCreditData['narration'] = 'Amount Rs ' . $arrPostData['extraPaidAmount'] . ' has credited from Advance of ' . $arrPostData['bill_number'];
                                        $arrCreditData['payment_amount'] = $arrPostData['extraPaidAmount'];
                                        $arrCreditData['account_name'] = $arrNonMemberIncome['billed_name'];
                                        $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveCreditAccountResponse', array('auth' => true, 'process' => 'fetch', 'soc_id' => $arrCreditData['soc_id'], 'id' => '', 'data' => $arrCreditData, 'user' => $auth['user_id'], 'username' => $auth['user_first_name'] . ' ' . $auth['user_last_name']));
                                        //                       / print_r($arrResponse);exit;
                                        if ($arrResponse['error']) {
                                            $this->soc_db_w->rollback();
                                            $err_msgs = 'Unbale to create credit not for nonmember ' . $arrNonMemberIncome['billed_name'];
                                            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember?page=".$page);
                                        }
                                    }
                                }
                                $intBookerLedgerDetails = $this->incometrackerevent->incometracker('nomemberincomelistner:payNonmemberBillLedger', array('auth' => $auth, 'postData' => $arrPostData, 'arrBookerLedgerDetails' => $arrBookerLedgerDetails, 'arrIncomeAccounts' => $arrIncomeAccounts));
                            }
                        } else {
                            $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'P', 'transaction_status' => 'complete'));

                            $this->soc_db_w->commit();
                            $successmsg = "payment is succesfully added to tracker waiting for clearance.";
                            $this->session->set("succ_msg", $successmsg);
                            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember?page=".$page);
                        }
                    }
                } else {
                    if (!empty($arrResponseTracker['payment_tracker_id'])) {
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
                    }

                    $this->soc_db_w->commit(); //commit all records
                    $errormsg = 'Payment transaction failed.';
                    $this->session->set("err_msg", $errormsg);
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
                }

                if (!empty($intBookerLedgerDetails)) {
                    //echo 'rajeshw';exit;
                    $trackertStatus = 'Y';
                    if (in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                        $trackertStatus = 'P';
                    }
                    if (!empty($arrResponseTracker['payment_tracker_id'])) {
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete'));
                    }
                    $this->soc_db_w->commit();
                    $successmsg = "payment done succesfully";
                    $this->session->set("succ_msg", $successmsg);
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember");
                } else {
                    $this->soc_db_w->rollback();
                    $err_msgs = 'Unbale to complete transaction, Please try later.';
                    if (!empty($arrResponseTracker['payment_tracker_id'])) {
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
                    }
                }
            } else {
                //collect form error messages
                foreach ($form->getMessages() as $message) {
                    $err_msgs[$message->getField()] = (string) $message;
                } // end of foreach form getMessages
            }
        }
        //exit;
        $this->view->setVar("page", $page);
        $this->view->setVar("form", $form);
        $this->view->setVar("options", $options);
        $this->view->setVar("err_msgs", $err_msgs);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("constants", $this->constants);
        $this->view->setVar("referer_url", $referer_url);
        $this->view->setVar("showBankAccount", $showBankAccount);
    }

    /**
     * member pay bill invoices list.
     * 
     * @method paymemberbillAction
     * @access public
     */
    public function paymemberbillAction($unit_id) {
        $arroptions = array();
        //echo '<pre>';//print_r($this->constants['payment_mode_for_clearance']);exit;
        if (empty($unit_id)) {
            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember");
        } elseif (!empty($unit_id) && is_numeric($unit_id)) {
            $arrIncomeInvoiceDetail = array();
            $auth = $this->session->get('auth');
            $page = $this->request->getQuery("page", "int");
            if(empty($page) && strstr($_SERVER['HTTP_REFERER'], '/incomemember?page='))
            {
                $page = array_pop(explode('page=', $_SERVER['HTTP_REFERER']));
                $page = !empty($page) ? $page : 1;
            }
//            /echo $page;exit;
            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $unit_id;
            $arrIncomeInvoiceMemberDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberDetail', $arrDataListener); //get all Unit details
            if (!empty($arrIncomeInvoiceMemberDetail)) {
                $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                $arroptions['received_from'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
            }
            $arrUnitDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', array('auth' => $auth, 'unit_id' => $unit_id)); //get all Unit details
            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceUnpaidBill', $arrDataListener); //get all Unit details

            $arrListenerdata = array('auth' => $auth);
            $this->incometrackerevent->addListener('generalSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
            $generalsettingparameter = $this->incometrackerevent->incometracker('generalSetting:getgeneralsetting', $arrListenerdata);
            $arroptions['generalsetting'] = $generalsettingparameter;
            $arroptions['billpaymentmode'] = $this->constants['billpaymentmode'];
            $total_unpaid_invoice_amount = 0;
            if (isset($arrIncomeInvoiceDetail['total_unpaid_invoice_amount'])) {
                $total_unpaid_invoice_amount = $arrIncomeInvoiceDetail['total_unpaid_invoice_amount'];
                unset($arrIncomeInvoiceDetail['total_unpaid_invoice_amount']);
            }
            if (isset($arrIncomeInvoiceDetail['latest_due_date'])) {
                unset($arrIncomeInvoiceDetail['latest_due_date']);
            }
            if (isset($arrIncomeInvoiceDetail['last_invoice_details'])) {
                unset($arrIncomeInvoiceDetail['last_invoice_details']);
            }
            $arrLastPeriodPaymentTransaction = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPreviousPaymentTransaction', array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'bill_type' => array('member', 'common_bill', 'common_bill_quickpay', 'creditaccount-member'), 'limit' => 3));
            $this->view->setVar("arrLastPeriodPaymentTransaction", $arrLastPeriodPaymentTransaction);
            // //print_r($generalsettingparameter['ALLOWED_PARTIAL_PAYMENT']);//exit;
            $arroptions['total_unpaid_amount'] = $total_unpaid_invoice_amount;
            $arroptions['bill_type'] = 'member';
            //if(!empty())
            $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id' => $auth['soc_id'])); //get all Unit details
            $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail' => $arrAccountDetail)); //get all Unit details
            $showBankAccount = 0;
            if (!empty($arrLedgerAccountDetail['arrBank'])) {
                $arroptions['bank_account'] = $arrLedgerAccountDetail['arrBank'];
                $arroptions['bank_selected'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                if (count($arrLedgerAccountDetail['arrBank']) > 1) {
                    $showBankAccount = 1;
                }
            }

            $form = new \ChsOne\IncomeTracker\Forms\PayBillForm($facility_booking, $arroptions);

            if ($this->request->isPost()) {

                $arrPostData = $this->request->getPost();
                $page = $arrPostData['page'];
                $arrPostData['member_paid_invoice'] = trim($arrPostData['member_paid_invoice'], ',');
                if (strtolower($generalsettingparameter['ALLOWED_PARTIAL_PAYMENT']) == 'no' || (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0)) {
                    $arrPostData['total_unpaid_amount'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberUnpaidDueByInvoice', array('arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'member_paid_invoice' => $arrPostData['member_paid_invoice']));
                }
                //For TDS adjustment
                if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                    $arrPostData['payment_amount'] = (float) $arrPostData['payment_amount'] + $arrPostData['tds_amount'];
                }
                //Do not create bill if financial years accounting closed    
                $arrPostData['arrCloseAccountPayment'] = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id' => $auth['soc_id'], 'bill_date' => $arrPostData['payment_date']));
                if ($form->isValid($arrPostData) != false) {
//                    //Do not create bill if financial years accounting closed    
//                    $arrClosedAccountDetail = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id'=>$auth['soc_id'], 'bill_date'=>$arrPostData['payment_date']));
//                    if( count($arrClosedAccountDetail)>0 ) {
//                        $this->session->set("err_rule", 'Accounting has been closed for this payment date.');
//                        return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember");
//                    }

                    $arrPostData['writeoff_amount'] = preg_replace('/\s+/', '', $arrPostData['writeoff_amount']);
                    $arrPostData['member_paid_invoice'] = trim($arrPostData['member_paid_invoice'], ',');
                    //echo '<pre>';//print_r($arrPostData);exit;
                    //Dynamically select unpaid invoices based on amount paid but not in case of writeoff
                    if (empty($arrPostData['writeoff_amount'])) {
                        $memberPaidInvoices = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberPaidInvoices', array('payment_amount' => $arrPostData['payment_amount'], 'unpaidInvoiceDetail' => $arrIncomeInvoiceDetail));
                        if (!empty($memberPaidInvoices)) {
                            $arrPostData['member_paid_invoice'] = trim(implode(',', $memberPaidInvoices), ',');
                        }
                    }

                    $arrgenerateInvoiceid = array('soc_id' => $auth['soc_id']);
                    $arrPostData['receipt_number'] = $this->incometrackerevent->incometracker('InvoiceGenerator:generate_receipt_id', $arrgenerateInvoiceid);
                    //echo '<pre>';print_r($arrPostData);exit;
                    //transaction in case of faliure revert all table entries
                    $arrResponseTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveInvoicePaymentTracker', array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'postData' => $arrPostData)); //get all Unit details
                    $arrPostData['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];

                    // if payment mode is cheque then entry will go to tracker    
                    //echo '<pre>';//print_r($arrResponseTracker);//print_r($arrPostData);
                    $arrPostData['soc_id'] = $auth['soc_id'];

                    $paymentToken = $this->incometrackerevent->incometracker('MemberIncomeDetail:generatePaymentToken', array('arrPaymentTracker' => $arrPostData)); //get all Unit details
                    $isTokenValid = $this->incometrackerevent->incometracker('MemberIncomeDetail:paymentTokenVerification', array('paymentToken' => $paymentToken)); //get all Unit details

                    $this->soc_db_w = $this->di->getShared('soc_db_w');
                    $this->soc_db_w->begin();

                    if ($isTokenValid && !empty($arrResponseTracker['payment_tracker_id'])) {
                        //Update the transaction status to in process
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'transaction_status' => 'in_process'));

                        if (in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                            if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {
                                if (isset($arrPostData['writeoff_amount']) && !empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                                    $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:updateUnitInvoiceStatus', array('soc_id' => $auth['soc_id'], 'status' => 'in_process', 'invoice_number' => $arrPostData['member_paid_invoice'])); //Update payment status to in_process
                                } else {
                                    $arrResponse = $arrResponseTracker;
                                }
                                $succMsg = ucwords($arrUnitDetail['soc_building_name']) . '/' . ucwords($arrUnitDetail['unit_flat_number']).' receipt has successfully added to receipt tracker waiting for clearance.';
                            }
                        } else {
                            //echo '<pre>';//print_r($arrPostData);exit;
                            if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {
                                if (isset($arrPostData['writeoff_amount']) && !empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                                    $arrResponse = $this->_invoiceWrtieoffPaymentTracker($unit_id, array('arrPostData' => $arrPostData, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                                } else {
                                    $arrResponse = $this->_invoicePaymentTracker($unit_id, array('arrPostData' => $arrPostData, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                                }
                                $succMsg = ucwords($arrUnitDetail['soc_building_name']) . ' / ' . ucwords($arrUnitDetail['unit_flat_number']) . ' receipt has added successfully';
                            }
                        }

                        //update the transaction status in payment tracker
                        //$this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id'=>$auth['soc_id'], 'payment_tracker_id'=>$arrResponseTracker['payment_tracker_id'], 'transaction_status'=>'complete'));
                    } else {
                        //$arrUpdatePaymentTrackerListener = array('soc_id'=>$auth['soc_id'], 'payment_tracker_id'=>$arrResponseTracker['payment_tracker_id'], 'status'=>'N');
                        if (!empty($arrResponseTracker['payment_tracker_id'])) {
                            $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
                        }

                        $this->soc_db_w->commit(); //commit all records
                        $this->session->set("err_rule", 'Unable to complete transaction, Please try later.');
                        return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember?page=" . $page);
                    }

                    //redirect after response
                    if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                        $trackertStatus = 'Y';
                        if (in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                            $trackertStatus = 'P';
                            if (strtolower($arrPostData['payment_mode']) == 'cheque' && $this->getDatabaseDate($arrPostData['payment_date']) > $this->getCurrentDate('database')) {
                                $trackertStatus = 'R'; //Update received cheque to pdc state
                            }
                        }
                        if (!empty($arrResponseTracker['payment_tracker_id'])) {
                            $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete'));
                        }
                        $this->soc_db_w->commit(); //commit all records
                        $this->session->set("succ_msg", $succMsg);
                        return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember?page=" . $page);
                    } else {
                        $this->soc_db_w->rollback(); //rollback in case of failure
                        $this->session->set("err_rule", 'Unbale to complete transaction, Please try later.');
                    }

                    if (empty($arrResponse) || (!empty($arrResponse) && strtolower($arrResponse['status']) != 'success')) {
                        if (!empty($arrResponseTracker['payment_tracker_id'])) {
                            $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
                        }
                    }
                } else {

                    //collect form error messages
                    foreach ($form->getMessages() as $message) {
                        $err_msgs[$message->getField()] = (string) $message;
                    } // end of foreach form getMessages
                }
            }
        }
        //echo '<pre>';//print_r($arrIncomeInvoiceDetail);exit;
        $this->view->setVar("arrIncomeInvoiceDetail", $arrIncomeInvoiceDetail);
        $this->view->setVar("arrUnitDetail", $arrUnitDetail);
        $this->view->setVar("allowed_partial_payment", $generalsettingparameter['ALLOWED_PARTIAL_PAYMENT']);
        $this->view->setVar("form", $form);
        $this->view->setVar("page", $page);
        $this->view->setVar("unit_id", $unit_id);
        $this->view->setVar("showBankAccount", $showBankAccount);
        //$this->view->setVar("options", $options);
        $this->view->setVar("err_msgs", $err_msgs);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("constants", $this->constants);
    }

    /**
     * member pay bill invoices list.
     * 
     * @method paymemberbillAction
     * @access public
     */
    public function invoiceWriteOffAction($unit_id) {
        //echo '<pre>';
        $arroptions = array();
        if (empty($unit_id)) {
            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember");
        } elseif (!empty($unit_id) && is_numeric($unit_id)) {
            $arrIncomeInvoiceDetail = array();
            $auth = $this->session->get('auth');
            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $unit_id;
            $arrIncomeInvoiceMemberDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberDetail', $arrDataListener); //get all Unit details
            if (!empty($arrIncomeInvoiceMemberDetail)) {
                $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                $arroptions['received_from'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
            }

            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceUnpaidBill', $arrDataListener); //get all Unit details
            $arrListenerdata = array('auth' => $auth);
            $this->incometrackerevent->addListener('generalSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
            $generalsettingparameter = $this->incometrackerevent->incometracker('generalSetting:getgeneralsetting', $arrListenerdata);
            $arroptions['generalsetting'] = $generalsettingparameter;
            $arroptions['billpaymentmode'] = $this->constants['billpaymentmode'];
            $total_unpaid_invoice_amount = 0;
            if (isset($arrIncomeInvoiceDetail['total_unpaid_invoice_amount'])) {
                $total_unpaid_invoice_amount = $arrIncomeInvoiceDetail['total_unpaid_invoice_amount'];
                unset($arrIncomeInvoiceDetail['total_unpaid_invoice_amount']);
            }
            $options['total_unpaid_amount'] = $arroptions['total_unpaid_amount'] = $total_unpaid_invoice_amount;
            $options['bill_type'] = $arroptions['bill_type'] = 'member';
            $form = new \ChsOne\IncomeTracker\Forms\PayBillForm($facility_booking, $arroptions);

            if ($this->request->isPost()) {

                if ($form->isValid($this->request->getPost()) != false) {
                    $arrPostData = $this->request->getPost();
                    //echo '<pre>';//print_r($arrPostData);exit;
                    //transaction in case of faliure revert all table entries
                    $this->soc_db_w = $this->di->getShared('soc_db_w');
                    $this->soc_db_w->begin();

                    $arrgenerateInvoiceid = array('soc_id' => $auth['soc_id']);
                    $arrPostData['receipt_number'] = $this->incometrackerevent->incometracker('InvoiceGenerator:generate_receipt_id', $arrgenerateInvoiceid);
                    $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveInvoicePaymentTracker', array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'postData' => $arrPostData)); //get all Unit details
                    if (strtolower($arrPostData['payment_mode']) == 'cash' && !empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                        $arrResponse = $this->_invoiceWrtieoffPaymentTracker($unit_id, array('arrPostData' => $arrPostData, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                    } else {
                        $arrUpdateUnitInvoiceListener = array('soc_id' => $auth['soc_id'], 'invoice_number' => $arrPostData['member_paid_invoice'], 'status' => 'in_process');
                        $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:updateUnitInvoiceStatus', $arrUpdateUnitInvoiceListener); //get all Unit details
                        //$arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveInvoicePaymentTracker', array('soc_id'=>$auth['soc_id'], 'unit_id'=>$unit_id, 'postData'=>$arrPostData)); //get all Unit details
                    }

                    //redirect after response
                    if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                        $this->soc_db_w->commit(); //commit in case of success
                        $this->session->set("succ_msg", VENDORBILLPAYMENT_SUCC_MSG);
                        return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember");
                    } else {
                        $this->soc_db_w->rollback(); //rollback in case of failure
                        $this->session->set("err_rule", 'Unable to complete transaction, Please try later.');
                    }
                } else {

                    //collect form error messages
                    foreach ($form->getMessages() as $message) {
                        $err_msgs[$message->getField()] = (string) $message;
                    } // end of foreach form getMessages
                }
            }
        }
        //        echo '<pre>hello';
        //        //print_r($arrIncomeInvoiceDetail);exit;
        $this->view->setVar("strIncomeInvoiceDetail", json_encode($arrIncomeInvoiceDetail));
        $this->view->setVar("arrIncomeInvoiceDetail", $arrIncomeInvoiceDetail);
        $this->view->setVar("form", $form);
        //$this->view->setVar("options", $options);
        $this->view->setVar("err_msgs", $err_msgs);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("constants", $this->constants);
    }

    private function getGeneralSetting() {
        $this->incometrackergeneralsettingevent = new IncometrackerEvent($this->config);
        $arrListenerdataForApplicableTaxes = array('auth' => $this->session->get("auth"));
        $arrListenerdataForApplicableTaxes['effective_date'] = ($ruleID != '') ? $arrRuleData['arrIncomeRuleDetails']['effective_date'] : $this->getCurrentDate('database');
        $this->incometrackergeneralsettingevent->addListener('InvoiceGenerator', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
        $arrApplicableTaxes = $this->incometrackergeneralsettingevent->incometracker('InvoiceGenerator:getAllSetting', $arrListenerdataForApplicableTaxes);
        return $arrApplicableTaxes;
    }

    /**
     *
     * @param type $unit_id        	
     * @param type $invoice_number        	
     * @param type $download        	
     * @param type $soc_id        	
     * @return type
     */
    public function downloadInvoiceAction($unit_id = '', $invoice_number = '', $download = '', $soc_id = '', $return = false) {


        $arrUnitId = json_decode($unit_id);

        // harshada k - 21-11-2018 -start
        $arrInvoiceType = array();
        $invoice_type = $this->request->get("invoice_type");
        
        if (!empty($invoice_type)) {
            if ($invoice_type == 'original') {
                $invoice_type_array = array('0' => 'Original');
            } elseif ($invoice_type == 'duplicate') {
                $invoice_type_array = array('0' => 'Original', '1' => 'Duplicate');
            }
        } else {
            $invoice_type_array = array('0' => 'Original');
        }        
        $arrInvoiceType['invoice_type'] = $invoice_type_array;
        //end

        if (empty($download)) {
            $download = 'download';
        }

        if (!empty($soc_id)) { //if soc_id is sent as get parameter
            $auth = ['soc_id' => $soc_id];
            $conce = $this->calMultiDbFlow($auth['soc_id']);
            $this->di->setShared('dbSoc', $conce);
        } else {
            $auth = $this->session->get('auth');
        }

        if (isset($arrUnitId) && !empty($arrUnitId) && is_array($arrUnitId)) {

            foreach ($arrUnitId as $unit_id) {
                $arrGetInvoiceData = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'order_by' => 'unit_invoice_id desc');
                $arrInvoiceData = $this->incometrackerevent->incometracker('InvoiceGenerator:getLastUnitInvoiceByUnit', $arrGetInvoiceData);

                $unitInvoiceDataArr['unit_id'] = $unit_id;
                $unitInvoiceDataArr['invoice_number'] = $arrInvoiceData['invoice_number'];
                $unitInvoiceDataArr['download'] = $download;
                $unitInvoiceDataArr['soc_building_name'] = $arrInvoiceData['soc_building_name'];
                $unitInvoiceDataArr['unit_name'] = $arrInvoiceData['unit_name'];

                $unitInvoiceData[] = $unitInvoiceDataArr;
            }
        } else if (!empty($invoice_number) && !empty($unit_id)) {

            $unitInvoiceDataArr['unit_id'] = $unit_id;
            $unitInvoiceDataArr['invoice_number'] = $invoice_number;
            $unitInvoiceDataArr['download'] = $download;

            $unitInvoiceData[] = $unitInvoiceDataArr;
        }

        if (empty($invoice_number) && empty($arrUnitId)) {
            $invoice_details = explode('/', $this->dncAction($unit_id));
            $unit_id = $invoice_details[1];
            $invoice_number = $invoice_details[2];
            $download = $invoice_details[3];
            $auth['soc_id'] = $soc_id = $invoice_details[4];
            if (!empty($soc_id)) { //if soc_id is sent as get parameter
                $auth = ['soc_id' => $soc_id];
                $conce = $this->calMultiDbFlow($auth['soc_id']);
                $this->di->setShared('dbSoc', $conce);
            } else {
                $auth = $this->session->get('auth');
            }
            $timestamp = (!empty($invoice_details[5]) ? $invoice_details[5] : time());
            //           echo '<br>'.time()."+++++++++++".$timestamp;exit();
            if (time() > $timestamp) {
                $this->session->set('succ_msg', 'Link has been expired. Try again after some time');
                if (!empty($this->session->get('auth'))) {
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/memberincomepaymentlist/");
                } else {
                    return $this->response->redirect($this->config->system->full_base_url . "users/linkExpired");
                }
            }

            $arrGetInvoiceData = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'order_by' => 'unit_invoice_id desc');
            $arrInvoiceData = $this->incometrackerevent->incometracker('InvoiceGenerator:getLastUnitInvoiceByUnit', $arrGetInvoiceData);

            $unitInvoiceDataArr['unit_id'] = $unit_id;
            $unitInvoiceDataArr['invoice_number'] = $invoice_number;
            $unitInvoiceDataArr['download'] = $download;
            $unitInvoiceDataArr['soc_building_name'] = $arrInvoiceData['soc_building_name'];
            $unitInvoiceDataArr['unit_name'] = $arrInvoiceData['unit_name'];

            $unitInvoiceData[] = $unitInvoiceDataArr;
        }

        if (!empty($auth)) {

            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $unit_id;
            $arrDataListener['invoice_number'] = $invoice_number;
            $arrDataListener['getSingleInvoiceDetail'] = true;
            $arrDataListener['show_cancelled_invoice'] = true;

            $arrAllUnitInvoiceDetail = array();
            $arrInvoiceSetting = array();
            $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
            $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
            $arrInvoiceSetting['totalInvoiceCount'] = count($arrUnitId);

            if (isset($unitInvoiceData) && !empty($unitInvoiceData)) {

                foreach ($unitInvoiceData as $arrDataValue) {

                    $unit_id = $arrDataValue['unit_id'];
                    $invoice_number = $arrDataValue['invoice_number'];

                    if (!empty($auth)) {

                        $defaultTableCoumnCount = 0;
                        $arrDataListener['soc_id'] = $auth['soc_id'];
                        $arrDataListener['unit_id'] = $unit_id;
                        $arrDataListener['invoice_number'] = $invoice_number;
                        $arrDataListener['getSingleInvoiceDetail'] = true;
                        $arrDataListener['show_cancelled_invoice'] = true;

                        $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceDetail', $arrDataListener); //get all Unit details
                        $arrMemberDetail = array();
                        $strMemberAssociateName = '';
                        if (!empty($arrIncomeInvoiceDetail)) {
                            $arrData = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id);
                            $units = new Units();
                            $arrMemberDetail = current($units->getUnitMemberDetailByUnitId($arrData));
                            $arrUnitParkingDetail = current($units->getUnitParkingDetails(array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id)));
                            if(!empty($arrUnitParkingDetail))
                            {
                                $arrMemberDetail['parking_number'] = $arrUnitParkingDetail['parking_number'];
                                $arrMemberDetail['parking_type'] = $arrUnitParkingDetail['parking_type'];
                                $arrMemberDetail['allotment_for'] = $arrUnitParkingDetail['allotment_for'];
                                unset($arrUnitParkingDetail);
                            }
                            //echo '<pre>';print_r($arrMemberDetail);exit;
                            if (empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['bill_to'])) {
                                $arrMemberAssociateDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitAllAssociateMemberDetails', $arrData); //get all Unit details
                                if (!empty($arrMemberAssociateDetail)) {
                                    $strMemberAssociateName = $this->incometrackerevent->incometracker('MemberIncomeDetail:formatAssociateMemberName', $arrMemberAssociateDetail); //get all Unit details
                                }
                            }
                        }
                    }
                    $this->incometrackerevent->addListener('incomeGeneralSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
                    $arrInvoiceGeneralSetting = $this->incometrackerevent->incometracker('incomeGeneralSetting:getgeneralsetting', array('soc_id' => $auth['soc_id']));
                    $defaultTableCoumnCount = 1;

                    //Remove zero amount particular from invoice
                    if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars']) && count($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars']) > 0) {
                        $arrIncomeAccounts = $this->incometrackerevent->incometracker('MemberIncomeDetail:getIncomeAccountDetails', array('soc_id' => $auth['soc_id'], 'arrAccountType' => array('member', 'nonmember'), 'order_by' => 'display_order_id', 'order' => 'asc')); //get all Unit details

                        $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceParticularDisplayOrder', array('soc_id' => $auth['soc_id'], 'arrInvoiceParticulars' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'], 'arrIncomeAccounts' => $arrIncomeAccounts)); //get all Unit details

                        $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:removeDeactivatedParticular', array('arrInvoiceParticular' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'])); //get all Unit details
                        //Get rule detail
                        if (isset($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) && strtolower($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) == 'yes') {
                            $defaultTableCoumnCount = 2;
                            //$this->view->setVar("showRateSqft", 1);
                            $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getRateCalculationChargesParticular', array('arrInvoiceParticular' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'], 'arrMemberDetail' => $arrMemberDetail, 'arrIncomeAccounts' => $arrIncomeAccounts));
                        }

                        //                if ($auth['soc_id'] == 26) {
                        //                    $defaultTableCoumnCount = 2;
                        //                    $this->view->setVar("showRateSqft", 1);
                        //                    $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getRuleAmountDetail', array('arrInvoiceParticular' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'], 'unit_type' => $arrMemberDetail['unit_type'])); //get all Unit details
                        //                } 
                        if ($auth['soc_id'] == 15) {
                            $defaultTableCoumnCount = 2;
                            //$this->view->setVar("showRateSqft", 1);                            
                            $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getFourDRuleAmountDetail', array('arrInvoiceParticular' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'], 'unit_type' => $arrMemberDetail['unit_type'])); //get all Unit details
                        }
                    }
                    ////print_r($arrIncomeInvoiceDetail);exit;
                    ////print_r($arrMemberDetail);exit;
                    $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
                    $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
                    //            $arrGeneralSettingData = array('soc_id'=>$auth['soc_id'], 'setting_key'=>array('INCOME_PAYMENT_INSTRUCTION','INCOME_SHOW_INTEREST_BREAKUP','INVOICE_LOGO','PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD','SHOW_SOCIETY_SIGNATURE','SHOW_CHSONE_FOOTER','PARTICULAR_AMOUNT_ROUNDOFF_TYPE','PARTICULAR_AMOUNT_ROUNDOFF'));
                    //            $arrInvoiceGeneralSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceGeneralSetting', $arrGeneralSettingData);//get all Unit details
                    //echo '<pre>';//print_r($arrIncomeInvoiceDetail);exit;
                    $arrAllTaxClass = array();
                    //            if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'])) {
                    //                foreach ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] as $key => $eachParticular) {
                    //                    if(!empty($eachParticular['tax_applicable']))
                    //                    {
                    //                        $arrTaxClass = unserialize($eachParticular['tax_applicable']);
                    //                        array_pop($arrTaxClass);
                    //                        if(!empty($arrAllTaxClass))
                    //                        {
                    //                            if(count($arrTaxClass)>1)
                    //                            {
                    //                                foreach($arrTaxClass as $keyT=>$eachTax)
                    //                                {
                    //                                    $tempIndex = count($arrAllTaxClass);
                    //                                    $arrAllTaxClass[$tempIndex] = $keyT;
                    //                                }
                    //                            }
                    //                            else
                    //                            {
                    //                                $tempIndex = count($arrAllTaxClass);
                    //                                $arrAllTaxClass[$tempIndex] = key($arrTaxClass);
                    //                            }
                    //                        }
                    //                        else
                    //                        {
                    //                            $arrAllTaxClass = array_keys($arrTaxClass);
                    //                        }
                    //                    }
                    //                    if(!empty($eachParticular['particular']))
                    //                    {
                    //                        $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'][$key]['particular'] = ucwords(preg_replace('/([A-Z])/', ' $1', $eachParticular['particular']));
                    ////                        if(strtolower($eachParticular['particular']) == 'noc')
                    ////                        {
                    ////                            $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'][$key]['particular'] = 'Non-Occupancy Charges';
                    ////                        }
                    //                    }
                    //                }
                    //            }
                    $arrGetTaxDetailParam = array('soc_id' => $auth['soc_id'], 'invoice_number' => $invoice_number);
                    if(!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['status']) && strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['status']) == 'cancelled')
                    {
                        $arrGetTaxDetailParam = array('soc_id' => $auth['soc_id'], 'invoice_number' => $invoice_number, 'showCancelledInvoice'=>true);
                    }
                    $arrAppliedTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getAppliedTaxDetail', $arrGetTaxDetailParam); //get all Unit details
                    $arrAllTaxClass = array();
                    $returntaxAmount = 0;

                    if (!empty($arrAppliedTaxDetail)) {
                        foreach ($arrAppliedTaxDetail as $eachTaxDetail) {
                            if (strtoupper($eachTaxDetail['tax_class']) == 'GST') {
                                $returntaxAmount += $eachTaxDetail['tax_amount'];
                            }
                            $arrAllTaxClass[] = $eachTaxDetail['tax_class'];
                        }

                        $arrTaxDetail = array();
                        if (!empty($arrAllTaxClass)) {
                            $arrAllTaxClass = array_unique($arrAllTaxClass);
                            $arrAllTaxClass = array_values($arrAllTaxClass);
                            //$strAllTaxClass = implode('","', $data['tax_class_name']);
                            $arrTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getTaxClassDetail', array('soc_id' => $auth['soc_id'], 'tax_class_name' => $arrAllTaxClass)); //get all Unit details
                            $arrAppliedTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getParticularTaxDetail', array('arrAppliedTaxDetail' => $arrAppliedTaxDetail));
                            $getInvoiceTaxAmount = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceTaxAmount', $arrGetTaxDetailParam);
                            $arrAllUnitInvoiceDetail[$unit_id]['invoiceTaxAmount'] = $getInvoiceTaxAmount;
                        }
                    } else {
                        $arrTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getTaxClassDetail', array('soc_id' => $auth['soc_id'], 'tax_class_name' => array('GST'))); //get all Unit details
                    }
                    //            /echo '<pre>';print_r($arrTaxDetail);exit;
                    $grandTotal = $finalInvoiceAmount = $outstandingPrinciplaAmount = $outstandingInterestAmount = $interestAmount = $advanceAmount = 0;
                    if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['finalInvoiceAmount'])) {
                        $finalInvoiceAmount = round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['finalInvoiceAmount'], 3);
                    }

                    if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_date'])) {
                        $invoiceDate = (strrchr($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_date'], '/')) ? $this->getDatabaseDate($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_date']) : $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_date'];
                        $arrLateChargesDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLatePaymentChargesByInvoiceDate', array('soc_id' => $auth['soc_id'], 'invoice_from_date' => $invoiceDate));
                        $arrAllUnitInvoiceDetail[$unit_id]['arrLateChargesDetail'] = $arrLateChargesDetail;
                    }

                    //echo $finalInvoiceAmount;exit;
                    $grandTotal = $finalInvoiceAmount;
                    if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount']) && $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'] > 0) {
                        $advanceAmount = round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'], 2);
//                if ($advanceAmount < $finalInvoiceAmount) {
//                    $grandTotal = round($finalInvoiceAmount - $advanceAmount, 2);
//                } else {
//                    $grandTotal = round($finalInvoiceAmount - $advanceAmount, 2);
//                }
                        $grandTotal = round($finalInvoiceAmount - $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['advance_amount'], 3);
                    } elseif (strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_first_invoice']) == strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_number'])) {
                        $outstandingPrincipalAmount = round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['principal_amount'], 2);
                        $grandTotal = round($finalInvoiceAmount + $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['principal_amount'], 3); //exit;
                    }
                    if ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['late_payment_interest'] > 0) {
                        $interestAmount = round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['late_payment_interest'], 2);
                        if (empty($grandTotal)) {
                            $grandTotal = round($finalInvoiceAmount + $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['late_payment_interest'], 3);
                        } else {
                            $grandTotal += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['late_payment_interest'];
                        }
                    }
                    //$returngrandTotal = $grandTotal = round($finalInvoiceAmount + $outstandingPrincipalAmount + $interestAmount, 2); //exit;
                    //Add Principal arrear
                    if ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'] > 0) {
                        $outstandingPrincipalAmount += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'];
                        $grandTotal += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'];
                    }
                    //Add Interest arrear
                    if ($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'] > 0) {
                        $outstandingInterestAmount = $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'];
                        $grandTotal += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'];
                    }
                    if (!empty($arrAppliedTaxDetail['lateChargeTotalTax']) && $arrAppliedTaxDetail['lateChargeTotalTax'] > 0) {
                        $grandTotal += $arrAppliedTaxDetail['lateChargeTotalTax'];
                    }
                    // round up grand total amount
                    //$arrGeneralInvoiceSetting  = $this->incometrackerevent->incometracker('generalSetting:getgeneralsetting', array('soc_id'=>$soc_id));
                    //       / echo 'rajesh<pre>';print_r($arrInvoiceGeneralSetting);exit;

                    $arrAllUnitInvoiceDetail[$unit_id]['showBankDetail'] = 0;
                    $arrAllUnitInvoiceDetail[$unit_id]['invoiceFontSize'] = 9;
                    $arrInvoiceSetting['showInvoiceDetail'] = 1;
                    if (!empty($arrInvoiceGeneralSetting)) {
                        if (!empty($arrInvoiceGeneralSetting['INCOME_PAYMENT_INSTRUCTION']) && isset($arrInvoiceGeneralSetting['INCOME_PAYMENT_INSTRUCTION'])) {
                            $arrInvoiceGeneralNote = explode(PHP_EOL, $arrInvoiceGeneralSetting['INCOME_PAYMENT_INSTRUCTION']);
                            $arrInvoiceSetting['arrInvoiceGeneralNote'] = $arrInvoiceGeneralNote;
                        }
                        if (!empty($arrInvoiceGeneralSetting['INCOME_SHOW_INTEREST_BREAKUP']) && isset($arrInvoiceGeneralSetting['INCOME_SHOW_INTEREST_BREAKUP']) && $arrInvoiceGeneralSetting['INCOME_SHOW_INTEREST_BREAKUP'] == 1) {
                            $arrInvoiceSetting['showInterestBreakup'] = 1;
                        }
                        if (!empty($arrInvoiceGeneralSetting['INVOICE_DPC']) && isset($arrInvoiceGeneralSetting['INVOICE_DPC']) && $arrInvoiceGeneralSetting['INVOICE_DPC'] == 'Yes') {
                            $arrInvoiceSetting['showDpcBreakup'] = 1;
                        }
                        if (!empty($arrInvoiceGeneralSetting['PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD']) && isset($arrInvoiceGeneralSetting['PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD']) && strtolower($arrInvoiceGeneralSetting['PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD']) == 'yes') {
                            $arrLastPeriodPaymentTransaction = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLastPeriodPaymentTransaction', array('soc_id' => $auth['soc_id'], 'arrInvoiceDetail' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]));
                            if (!empty($arrLastPeriodPaymentTransaction['payment_transaction_detail']) && count($arrLastPeriodPaymentTransaction['payment_transaction_detail']) > 0) {
                                $arrAllUnitInvoiceDetail[$unit_id]['arrLastPeriodPaymentTransaction'] = $arrLastPeriodPaymentTransaction;
                            }
                        }
                        if (!empty($arrInvoiceGeneralSetting['INVOICE_LOGO']) && isset($arrInvoiceGeneralSetting['INVOICE_LOGO']) && strtolower($arrInvoiceGeneralSetting['INVOICE_LOGO']) == 'yes') {
                            $arrSocietyWebsiteWelcome = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyWebsiteWelcome', array('soc_id' => $auth['soc_id']));
                            if (!empty($arrSocietyWebsiteWelcome['soc_header_logo'])) {
                                $arrInvoiceSetting['imageLogoUrl'] = $this->config->s3server_details->s3_security_protocol . $this->config->s3server_details->bucket_name . '.s3.amazonaws.com/socweb/' . $auth['soc_id'] . '/sitelogo/' . $arrSocietyWebsiteWelcome['soc_header_logo'];
                            }
                        }
                        if (!empty($arrInvoiceGeneralSetting['SHOW_SOCIETY_SIGNATURE']) && isset($arrInvoiceGeneralSetting['SHOW_SOCIETY_SIGNATURE']) && strtolower($arrInvoiceGeneralSetting['SHOW_SOCIETY_SIGNATURE']) == 'yes') {
                            $arrInvoiceSetting['showSocietySignature'] = true;
                        }
                        if (!empty($arrInvoiceGeneralSetting['SHOW_CHSONE_FOOTER']) && isset($arrInvoiceGeneralSetting['SHOW_CHSONE_FOOTER']) && strtolower($arrInvoiceGeneralSetting['SHOW_CHSONE_FOOTER']) == 'yes') {
                            $arrInvoiceSetting['showChsoneFooter'] = true;
                        }
                        if (!empty($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) && isset($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) && strtolower($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) == 'yes') {
                            $arrBankDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getDefaultBankAccount', array('soc_id' => $auth['soc_id']));
                            $arrInvoiceSetting['showBankDetail'] = 1;
                            $arrInvoiceSetting['arrBankDetail'] = $arrBankDetail;
                            if (count($arrAppliedTaxDetail['arrTaxParticular']) == 0 && (empty($finalInvoiceAmount) || strtolower($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) == 'no')) {
                                $defaultTableCoumnCount++;
                                $arrAllUnitInvoiceDetail[$unit_id]['showBankWithoutTaxParitcular'] = $defaultTableCoumnCount;
                            }
                        }
                        if (!empty($arrInvoiceGeneralSetting['INVOICE_FONT_SIZE']) && isset($arrInvoiceGeneralSetting['INVOICE_FONT_SIZE'])) {
                            $arrInvoiceSetting['invoiceFontSize'] = $arrInvoiceGeneralSetting['INVOICE_FONT_SIZE'];
                        }
                        if (!empty($arrInvoiceGeneralSetting['INVOICE_AMOUNT_ROUNDOFF']) && strtolower($arrInvoiceGeneralSetting['INVOICE_AMOUNT_ROUNDOFF']) == 'yes') {
                            $arrInvoiceSetting['isInvoiceRoundOff'] = 1;
                        }

                        if ($auth['soc_id'] == 15 || (isset($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) && strtolower($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) == 'yes')) {
                            $arrInvoiceSetting['showRateSqft'] = 1;
                        }
                    }
                    //Convert rupees from number to word
                    $grandTotal = (float) round($grandTotal, 2);
                    $rupeesInWord = '';
                    $rupeesInWord = $this->incometrackerevent->incometracker('MemberIncomeDetail:numberToWordRupees', array('number' => abs($grandTotal)));
                    $rupeesInWord = str_replace("  ", " ", $rupeesInWord);

                    if ($auth['soc_id'] == 38) {
                        $arrAllUnitInvoiceDetail[$unit_id]['hideParkingUnit'] = 1;
                    }
                    //echo '<pre>';print_r($arrAppliedTaxDetail);exit;
                    if (strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_first_invoice']) == strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_number'])) {
                        $arrPendingOutstanding = $this->incometrackerevent->incometracker('InvoiceGenerator:getPendingOutstanding', array('soc_id' => $auth['soc_id'], 'unit_id' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_id'], 'bill_type' => 'maintenance'));
                        if ($arrPendingOutstanding['delayed_payment_charges'] > 0) {
                            $outstandingInterestAmount = $arrPendingOutstanding['interest_amount'];
                            $interestAmount = $arrPendingOutstanding['delayed_payment_charges'];
                        } else {
                            $outstandingInterestAmount = $interestAmount;
                            $interestAmount = $arrPendingOutstanding['delayed_payment_charges'];
                        }
                    }

                    $dpclogs = \ChsOne\Models\LateChargeLog::find("for_invoice = '$invoice_number'");
                    $lateChargeLogs = $dpclogs->toArray();

                    $totalAmt = 0;
                    $totalInt = 0;
                    foreach ($lateChargeLogs as $dpclogData) {
                        $totalAmt += $dpclogData->chargeable_amount;
                        $totalInt += $dpclogData->interest_amount;
                    }


                    $arrAllUnitInvoiceDetail[$unit_id]['taxColumnCount'] = count($arrAppliedTaxDetail['arrTaxParticular']);
                    $arrAllUnitInvoiceDetail[$unit_id]['originalColumnCount'] = $defaultTableCoumnCount;
                    $arrAllUnitInvoiceDetail[$unit_id]['defaultColumnCount'] = $defaultTableCoumnCount;
                    $arrAllUnitInvoiceDetail[$unit_id]['arrTaxDetail'] = $arrTaxDetail;
                    $arrAllUnitInvoiceDetail[$unit_id]['arrLateChargeTaxClass'] = $arrAppliedTaxDetail['arrLateChargeTaxClass'];
                    $arrAllUnitInvoiceDetail[$unit_id]['arrAppliedTaxDetail'] = $arrAppliedTaxDetail['arrTaxClass'];
                    $arrAllUnitInvoiceDetail[$unit_id]['arrAppliedTaxCategory'] = $arrAppliedTaxDetail['arrTaxParticular'];
                    $arrAllUnitInvoiceDetail[$unit_id]['arrTaxCategoryParticular'] = $arrAppliedTaxDetail['arrTaxCategoryParticular'];
                    $arrAllUnitInvoiceDetail[$unit_id]['arrTaxCategoryParticularSubtotal'] = $arrAppliedTaxDetail['arrTaxCategoryParticularSubtotal'];
                    $arrAllUnitInvoiceDetail[$unit_id]['lateChargeTotalTax'] = $arrAppliedTaxDetail['lateChargeTotalTax'];
                    $arrAllUnitInvoiceDetail[$unit_id]['arrAllAppliedTaxDetail'] = $arrAppliedTaxDetail;
                    $arrAllUnitInvoiceDetail[$unit_id]['invoiceTotalAmount'] = number_format(round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['totalInvoiceAmount'] + $getInvoiceTaxAmount + $interestAmount, 2), 2, '.', '');
                    $arrAllUnitInvoiceDetail[$unit_id]['grandTotalAmount'] = number_format(round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['totalInvoiceAmount'] + $getInvoiceTaxAmount + $interestAmount + $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['roundoff_amount'] - $advanceAmount, 2), 2, '.', '');
                    $arrAllUnitInvoiceDetail[$unit_id]['finalInvoiceAmount'] = $finalInvoiceAmount;
                    $arrAllUnitInvoiceDetail[$unit_id]['outstandingPrincipalAmount'] = $outstandingPrincipalAmount;
                    $arrAllUnitInvoiceDetail[$unit_id]['outstandingInterestAmount'] = $outstandingInterestAmount;
                    $arrAllUnitInvoiceDetail[$unit_id]['interestAmount'] = $interestAmount;
                    $arrAllUnitInvoiceDetail[$unit_id]['advanceAmount'] = $advanceAmount;
                    $arrAllUnitInvoiceDetail[$unit_id]['associateMembers'] = $strMemberAssociateName;
                    $arrAllUnitInvoiceDetail[$unit_id]['balanceDue'] = $grandTotal;
                    $arrAllUnitInvoiceDetail[$unit_id]['rupeesInWord'] = ucwords($rupeesInWord);
                    $arrAllUnitInvoiceDetail[$unit_id]['arrIncomeInvoiceDetail'] = $arrIncomeInvoiceDetail['unit_invoice_detail'];
                    $arrAllUnitInvoiceDetail[$unit_id]['arrMemberDetail'] = $arrMemberDetail;
                    $arrAllUnitInvoiceDetail[$unit_id]['arrSocietyDetail'] = $arrSocietyDetail;
                    $arrAllUnitInvoiceDetail[$unit_id]['arrInvoiceGeneralNote'] = $arrInvoiceGeneralNote;
                    $arrAllUnitInvoiceDetail[$unit_id]['arrBankDetail'] = $arrBankDetail;
                    $arrAllUnitInvoiceDetail[$unit_id]['arrInvoiceType'] = $arrInvoiceType;
                    $arrAllUnitInvoiceDetail[$unit_id]['arrLatePaymentStack'] = $lateChargeLogs;
                    $arrAllUnitInvoiceDetail[$unit_id]['latePaymentamount'] = $totalAmt;
                    $arrAllUnitInvoiceDetail[$unit_id]['intrestPrinciple'] = $totalInt;
                }
            }

            if (!empty($download) && $download == 'downloadview') {
                $this->view->pick('incomedetails/downloadviewInvoice');
            } else if (!empty($download) && $download == 'download') {
                $arrListenerdata['format'] = 'pdf';
                //$arrListenerdata['unit_id'] = $unit_id;
                //$arrListenerdata['invoice_number'] = $invoice_number;
                $arrListenerdata['soc_id'] = $auth['soc_id'];
                $arrListenerdata['soc_name'] = $arrSocietyDetail['soc_name'];
                //$arrListenerdata['unit_detail'] = $arrMemberDetail;
                $arrListenerdata['invoice_type'] = $invoice_type; // harshada k - 21-11-2018
                $arrListenerdata['unitInvoiceData'] = $unitInvoiceData;
                $result = $this->invoiceexport->exportDocument('InvoiceExport:pdfDocs', $arrListenerdata);
            }

            if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_id'])) {
                //Get mongo entries
                $mongoData = array();
                $mongoData['id'] = $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_id'];
                $mongoData['module'] = 'income_unit_invoices';
                $mongoData['primary_key'] = 'unit_invoice_id';
                $userDetails = \ChsOne\Helper\CommonHelper::showUserDataForChangeLog($mongoData);
//            /print_r();
                $this->view->setVar("userDetails", $userDetails);
            }            

            $this->view->setVar("arrAllUnitInvoiceDetail", $arrAllUnitInvoiceDetail);
            $this->view->setVar("arrInvoiceSetting", $arrInvoiceSetting);
            $this->view->setVar("arrSocietyDetail", $arrSocietyDetail);
            $this->view->setVar("arrInvoiceType", $arrInvoiceType); //

            $this->view->setVar("config", $this->config);
            $this->view->setVar("type", 'print');
            $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
            $this->view->setLayout("printInvoice");
        }
    }

    /**
     * member income account.
     * 
     * @method generatebillAction
     * @access public
     * @uses \ChsOne\Models\Units Units Model
     */
    public function generatebill1Action($unit_id) {
        $parts = parse_url($_SERVER['HTTP_REFERER']);
        parse_str($parts['query'], $query);
        $page = '';
        if (isset($query['page']) && !empty($query['page'])) {
            $page = '?page=' . $query['page'];
        }

        $auth = $this->session->get('auth');
        if (empty($auth)) {
            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
        }
        $arrDataListener = array('soc_id' => $auth['soc_id']);
        $arrInvoiceSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceSetting', $arrDataListener); //get all Unit details

        $errmsg = $succmsg = '';
        if (!empty($arrInvoiceSetting)) {
            $arrDateRange = array();
            $invoiving_frequency = $arrInvoiceSetting['invoicing_frequency'];
            $start_date = $arrInvoiceSetting['effective_date'];
            $end_date = $this->getDatabaseDate(date('t/m/Y'));
            //$getNumberOfMonths  = $this->incometrackerevent->incometracker('InvoiceGenerator:getNumberOfMonths', array('start_date'=>$start_date, 'end_date'=>$end_date));//get all Unit details 
            switch (strtolower($invoiving_frequency)) {
                case 'quarterly':
                    $frequency = 3;
                    break;
                case 'half_yearly':
                    $frequency = 6;
                    break;
                case 'yearly':
                    $frequency = 12;
                    break;
                case 'monthly':
                default:
                    $frequency = 1;
                    break;
            }
            $arrDateRange = $this->incometrackerevent->incometracker('InvoiceGenerator:getDateRangeFrequencyWise', array('start_date' => $start_date, 'end_date' => $end_date, 'frequency' => $frequency)); //get all Unit details 
            if (!empty($arrDateRange)) {

                $i = 0;

                foreach ($arrDateRange as $eachDateRange) {
                    $date = explode('_', $eachDateRange);
                    //                    if ($i == 0) {
                    //                        //$date[0] = $this->getCurrentDate('database');
                    //                    }
                    $i++;
                    $start_date = $date[0];
                    $end_date = $date[1];
                    $soc_id = $auth['soc_id'];
                    $flagtrial = 'live';

                    if (empty($this->logger)) {
                        $strLogFolderPath = APP_PATH . "var/logs/" . $soc_id . "/invoicegenerate";
                        if (!is_dir($strLogFolderPath)) {
                            mkdir($strLogFolderPath, 0777, true);
                        }
                        $strLogFilePath = $strLogFolderPath . '/' . $this->strLogFilePath;
                        $this->logger = new FileAdapter($strLogFilePath);
                    }

                    $arrGenerateInvoiceData = array('soc_id' => $soc_id, 'unit_id' => $unit_id, 'start_date' => $start_date, 'end_date' => $end_date, 'action' => $postData['action_type'], 'flagtrial' => $flagtrial, 'logger' => $this->logger, 'auth' => $this->auth);
                    $arrInvoiceGeneratedResponse = $this->incometrackerevent->incometracker('AutoInvoice:generateInvoiceForSingleUnit', $arrGenerateInvoiceData);

                    if (!empty($arrInvoiceGeneratedResponse)) {
                        if (!empty($arrInvoiceGeneratedResponse['error']['value'])) {
                            $errmsg .= $arrInvoiceGeneratedResponse['error']['message'];
                            if ($arrInvoiceGeneratedResponse['error']['value'] == 2) {
                                break;
                            } else {
                                $errmsg .= '<br>';
                                continue;
                            }
                        } elseif (!empty($arrInvoiceGeneratedResponse['success']['value']) && $arrInvoiceGeneratedResponse['success']['value'] == 1) {
                            $succmsg = $arrInvoiceGeneratedResponse['success']['message'];
                        }
                    }
                }
            } else {
                $errmsg .= 'Unable to generete invoice, ' . ucfirst($invoiving_frequency) . ' frequency not completed yet.<br>';
            }
        } else {
            $errmsg .= 'Please set General Invoice Setting first to generate invoice';
        }
        //set error msg for invoice failure
        if (!empty($errmsg)) {
            $this->session->set("err_rule", $errmsg);
        }

        //set success msg for invoice success
        if (!empty($succmsg)) {
            $this->session->set("succ_msg", $succmsg);
            return $this->response->redirect($this->config->system->full_base_url . "income-details/memberincomepaymentlist/" . $unit_id);
        }
        return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
    }

    /**
     *
     * @method : addoutstanding amount
     */
    public function addoutstandingAction($unit_id = '') {
        //        echo 'soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND unit_id = "' . $unit_id . '"';exit();
        $parts = parse_url($_SERVER['HTTP_REFERER']);
        parse_str($parts['query'], $query);
        $page = '';
        if (isset($query['page']) && !empty($query['page'])) {
            $page = '?page=' . $query['page'];
        }
        $current_date = $this->getCurrentDate('database');
        $unitDetails = Units::findFirst('soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND unit_id = "' . $unit_id . '" AND (cancel_date = "0000-00-00" OR cancel_date > "' . $current_date . '")');
        $units = IncomeInvoiceAdjustment::findFirst('soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND fk_unit_id = "' . $unit_id . '" AND bill_type = "maintenance"');

        if (!empty($units)) {
            $options['id'] = $units->id;
            $options['unit_id'] = $units->fk_unit_id;
            $options['principal_amount'] = $units->principal_amount;
            $options['interest_amount'] = $units->interest_amount;
            $options['delayed_payment_charges'] = $units->delayed_payment_charges;
            $options['adjustment_type'] = $units->adjustment_type;
        } else {
            $options = [];
        }
        $this->view->setVar('adjustmentType', $units->adjustment_type);
        $form = new AddoutstandingForm(NULL, $options);
            
        if ($this->request->isPost()) {
            $request = $this->request->getPost();
//            /$form = new AddoutstandingForm(NULL, $options);
            if ($form->isValid($request) != false) {
                if ($request['id'] != '') {
                $incomeInvoiceAdjustmentModel = IncomeInvoiceAdjustment::findFirst('soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND id = "' . $request['id'] . '"');
                $incomeInvoiceAdjustmentModel->updated_date = $this->getCurrentDate('database');
                $incomeInvoiceAdjustmentModel->updated_by = $this->session->get('auth')['user_id'];
                } else {
                    $incomeInvoiceAdjustmentModel = new IncomeInvoiceAdjustment();
                    $incomeInvoiceAdjustmentModel->updated_date = $this->getCurrentDate('database');
                    $incomeInvoiceAdjustmentModel->updated_by = $this->session->get('auth')['user_id'];
                }
                //                echo $request['adjustment_type'];exit();
                $incomeInvoiceAdjustmentModel->adjustment_type = $request['adjustment_type'];

                $incomeInvoiceAdjustmentModel->soc_id = $this->session->get('auth')['soc_id'];
                $incomeInvoiceAdjustmentModel->fk_unit_id = $request['unit_id'];
                $incomeInvoiceAdjustmentModel->bill_type = 'maintenance';
                $incomeInvoiceAdjustmentModel->principal_amount = (float) filter_var($request['outstanding_amt'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);


                $incomeInvoiceAdjustmentModel->interest_amount = (float) filter_var($request['interest_amt'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                $incomeInvoiceAdjustmentModel->delayed_payment_charges = 0;
                if(strtolower($request['adjustment_type']) == 'outstanding' && !empty($request['delayed_payment_charges']) && $request['delayed_payment_charges']>0)
                {
                   $this->incometrackerevent->addListener('incomeGeneralSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
                   $arrGeneralInvoiceSetting  = $this->incometrackerevent->incometracker('incomeGeneralSetting:getgeneralsetting', array('soc_id'=>$this->session->get('auth')['soc_id']));
                   $request['delayed_payment_charges'] = $this->incometrackerevent->incometracker('InvoiceGenerator:getParticularRoundCharges', array('soc_id'=>$this->session->get('auth')['soc_id'],'particularAmount'=>$request['delayed_payment_charges'], 'arrGeneralInvoiceSetting'=>$arrGeneralInvoiceSetting));
                   $incomeInvoiceAdjustmentModel->delayed_payment_charges = (float) filter_var($request['delayed_payment_charges'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                }
               // echo $incomeInvoiceAdjustmentModel->delayed_payment_charges;exit;
                $incomeInvoiceAdjustmentModel->is_generated = 0;
                $incomeInvoiceAdjustmentModel->created_date = $this->getCurrentDate('database');
                $incomeInvoiceAdjustmentModel->created_by = $this->session->get('auth')['user_id'];

                //update ledger
                $ledgertxn = \ChsOne\Models\LedgerTxn::findFirst('soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND ledger_account_id = "' . $unitDetails->ledger_account_id . '" AND is_opening_balance = 1');
                //echo 'soc_id = "'.$this->session->get('auth')['soc_id'].'" AND ledger_account_id = "'.$unitDetails->ledger_account_id.'" AND is_opening_balance = 1';exit();
                if (!empty($ledgertxn)) {
                    $ledgertxn->transaction_type = 'dr';
                    $ledgertxn->transaction_date = $this->getDatabaseDate($request['opening_balance_date']);
                    if ($request['adjustment_type'] != 'outstanding') {
                        $request['interest_amt'] = 0;
                        $incomeInvoiceAdjustmentModel->interest_amount = 0;
                        $ledgertxn->transaction_type = 'cr';
                    }

                    $ledgertxn->transaction_amount = (float) filter_var($request['outstanding_amt'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) + (float) filter_var($request['interest_amt'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                    if (!empty($ledgertxn)) {
                        $ledgertxn->save();
                    } else {
                        $message = "Please specify ledgers for unit";
                        $this->session->set('err_rule', $message);
                        return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
                    }
                } else {
                    $message = "Please try later, ledger is not mapped with unit.";
                    $this->session->set('err_rule', $message);
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
                }

                if (!$incomeInvoiceAdjustmentModel->save()) {
                    $message = "Sorry!! Outstanding amount can't be added to account.";
                    $this->session->set('err_rule', $message);
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
                } else {
                    $message = "Outstanding amount added to account successfully";
                    $this->session->set('succ_msg', $message);
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
                }
            }
            else 
            {
                //collect form error messages
                foreach ($form->getMessages() as $message) {
                    $err_msgs[$message->getField()] = (string) $message;
                } // end of foreach form getMessages
                if(!empty($err_msgs))
                {
                    $this->session->set('err_rule', implode(' ',$err_msgs));
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
                }
            }
        }
        else
        {
            $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);
        }
        
        $this->view->setVar("form", $form);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("constants", $this->constants);

        $this->view->setVar("unit_name", $unitDetails->soc_building_name . "-" . $unitDetails->soc_building_floor . "-" . $unitDetails->unit_flat_number);
        $this->view->setVar("unit_id", $unit_id);
    }

    /**
     * function to check if outstanding amount is added for given unit
     */
    public function _isGeneratedOutstanding($unit_id) {
        //        echo 'soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND fk_unit_id = "' . $unit_id . '"';exit();
        $outstandingAmt = IncomeInvoiceAdjustment::findFirst('soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND fk_unit_id = "' . $unit_id . '"');
        $totalInvoiceAlreadyGenerated = \ChsOne\Models\UnitsInvoice::count('soc_id = "' . $this->session->get('auth')['soc_id'] . '" AND fk_unit_id ="' . $unit_id . '" AND status != "cancelled"');
        //        echo $totalInvoiceAlreadyGenerated;exit();
        $isAlreadyGenerated = ($totalInvoiceAlreadyGenerated > 0) ? 1 : 0;
        //        echo $isAlreadyGenerated;exit();
        if (!empty($outstandingAmt) && is_object($outstandingAmt)) {
            $outstandingAmt = $outstandingAmt->toArray();
            //            //print_r($outstandingAmt);
            $return['count'] = count($outstandingAmt);
            $return['is_generated'] = $outstandingAmt['is_generated'];
            $return['advance_amount'] = (empty($outstandingAmt['is_generated']) && strtolower($outstandingAmt['adjustment_type']) == 'advance' && strtolower($outstandingAmt['bill_type']) == 'maintenance') ? $outstandingAmt['principal_amount'] : 0;
            $return['outstanding_amount'] = (empty($outstandingAmt['is_generated']) && strtolower($outstandingAmt['adjustment_type']) == 'outstanding' && strtolower($outstandingAmt['bill_type']) == 'maintenance') ? $outstandingAmt['principal_amount'] + $outstandingAmt['interest_amount'] + $outstandingAmt['delayed_payment_charges'] : 0;
        }
        $return['is_already_generated'] = $isAlreadyGenerated;
        //echo '<pre>'; //print_r($return);
        return $return;
    }

    public function generatemanualbillAction($unit_id = '') {
        $auth = $this->session->get('auth');
        $unit_id = json_decode($unit_id);
        $previewUnitId = array();
        $previewUnitId = (is_array($unit_id)) ? $unit_id : array($unit_id);
        $unit_id = (is_array($unit_id)) ? current($unit_id) : $unit_id;

        $arrListenerdata = array('auth' => $auth, "unit_id" => $unit_id);
        $arrDataListener = array('soc_id' => $auth['soc_id']);
        $arrInvoiceSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceSetting', $arrDataListener); //get all Unit details
        //        //print_r($arrInvoiceSetting);exit();
        $frequency = 1;
        $soc_id = $auth['soc_id'];

        if (!empty($arrInvoiceSetting)) {
            switch (strtolower($arrInvoiceSetting['invoicing_frequency'])) {
                case 'quarterly':
                    $frequency = 3;
                    break;
                case 'half_yearly':
                    $frequency = 6;
                    break;
                case 'yearly':
                    $frequency = 12;
                    break;
                case 'monthly':
                default:
                    $frequency = 1;
                    break;
            }
        }

        $form = new GenerateManualBillForm(NULL, array());
        
        if ($this->request->isPost()) {
            //            ///print_r($this->request->getPost());exit;
            if ($form->isValid($this->request->getPost()) != false) {
                $postData = $this->request->getPost();
                $start_date = $this->getDatabaseDate($postData['start_date']);
                $end_date = $this->getDatabaseDate($postData['end_date']);
                $bill_date = $this->getDatabaseDate($postData['bill_date']);
                //                echo $start_date;echo $end_date;
                //                //print_r($postData);
                //               
                $unit_id = $postData['unit_id'];
                $flagtrial = 'live';

                if (empty($this->logger)) {
                    $strLogFolderPath = APP_PATH . "var/logs/" . $soc_id . "/invoicegenerate";
                    if (!is_dir($strLogFolderPath)) {
                        mkdir($strLogFolderPath, 0777, true);
                    }
                    $strLogFilePath = $strLogFolderPath . '/' . $this->strLogFilePath;
                    $this->logger = new FileAdapter($strLogFilePath);
                }

                $arrGenerateInvoiceData = array('soc_id' => $soc_id, 'unit_id' => $unit_id, 'start_date' => $start_date, 'end_date' => $end_date, 'action' => $postData['action_type'], 'bill_date' => $bill_date, 'flagtrial' => $flagtrial, 'logger' => $this->logger, 'auth' => $this->auth, 'socAdminUsers' => $this->session->get("socUsers")['socAdminUsers']);
                $arrInvoiceGeneratedResponse = $this->incometrackerevent->incometracker('AutoInvoice:generateInvoiceForSingleUnit', $arrGenerateInvoiceData);

                if (!empty($arrInvoiceGeneratedResponse['success']['value']) && $arrInvoiceGeneratedResponse['success']['value'] == 1) {
                    $this->session->set("succ_msg", $arrInvoiceGeneratedResponse['success']['message']);
                } elseif (!empty($arrInvoiceGeneratedResponse['error']['value'])) {
                    $this->session->set("err_rule", $arrInvoiceGeneratedResponse['error']['message']);
                }

                if (isset($postData['action']) && !empty($postData['action']) && strtolower($postData['action']) == 'ajax') {
                    exit();
                } else {
                    // page redirection
                    $parts = parse_url($_SERVER['HTTP_REFERER']);
                    parse_str($parts['query'], $query);
                    $page = '';
                    if (isset($query['page']) && !empty($query['page'])) {
                        $page = '?page=' . $query['page'];
                    }
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
                }
            }
            else
            {
                // collect form error messages
                foreach ($form->getMessages() as $message) {
                    $err_msgs[$message->getField()] = (string) ucfirst(trim($message));
                } // end of foreach form getMessages
                $this->session->set("err_rule", implode(',',$err_msgs));
                // page redirection
                $parts = parse_url($_SERVER['HTTP_REFERER']);
                parse_str($parts['query'], $query);
                $page = '';
                if (isset($query['page']) && !empty($query['page'])) {
                    $page = '?page=' . $query['page'];
                }
                return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember" . $page);
            }
        }

        $date = $this->getCurrentDate('display');
        $arrGetInvoiceData = array('soc_id' => $soc_id, 'unit_id' => $unit_id, 'order_by' => 'unit_invoice_id desc');
        //        echo '<pre>';//print_r($arrGetInvoiceData);exit();
        $arrInvoiceData = $this->incometrackerevent->incometracker('InvoiceGenerator:getLastUnitInvoiceByUnit', $arrGetInvoiceData);
        //echo '<pre>';print_r($arrInvoiceData);exit();
        $arrUnitData = array();
        if (!empty($arrInvoiceData)) {
            $date = (new \DateTime($arrInvoiceData['to_date']))->modify('first day of next month');
            $date = $date->format('Y-m-d');
            $date = $this->getDisplayDate($date);

            if (!empty($arrInvoiceData['from_date'])) {
                $arrInvoiceData['from_date'] = $this->getDisplayDate($arrInvoiceData['from_date']);
            }
            if (!empty($arrInvoiceData['to_date'])) {
                $nextInvoiceStartDate = (new \DateTime($arrInvoiceData['to_date']))->modify('+1 day');
                $nextInvoiceStartDate = $nextInvoiceStartDate->format('Y-m-d');
                $nextInvoiceStartDate = $this->getDisplayDate($nextInvoiceStartDate);

                $arrInvoiceData['to_date'] = $this->getDisplayDate($arrInvoiceData['to_date']);
            }
        } elseif (!empty($arrInvoiceSetting['effective_date'])) {
            $nextInvoiceStartDate = $this->getDisplayDate($arrInvoiceSetting['effective_date']);
        }
        if (count($previewUnitId) > 1) {
            $arrUnitData['unit_name'] = 'unit/preview';
        } else {
            if (empty($arrInvoiceData)) {
                $arrUnitData = $this->incometrackerevent->incometracker('InvoiceGenerator:getUnitDetailById', array('auth' => array('soc_id' => $soc_id), 'unit_id' => $unit_id));
                if (!empty($arrUnitData)) {
                    $arrUnitData['unit_name'] = ucwords($arrUnitData['soc_building_name'] . '/' . $arrUnitData['unit_flat_number']);
                }
            } else {
                $arrInvoiceData['unit_name'] = str_replace($arrInvoiceData['soc_building_name'] . '-', '', $arrInvoiceData['unit_name']);
                $arrInvoiceData['unit_name'] = ucwords($arrInvoiceData['soc_building_name'] . '/' . $arrInvoiceData['unit_name']);
            }
        }
        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("form", $form);
        $this->view->setVar("arrInvoiceSetting", $arrInvoiceSetting);
        $this->view->setVar("frequency", $frequency);
        $this->view->setVar("unit_id", $unit_id);
        $this->view->setVar("previewUnitId", $previewUnitId);
        $this->view->setVar("default_invoice_date", $nextInvoiceStartDate);
        $this->view->setVar("arrLastInvoiceData", $arrInvoiceData);
        $this->view->setVar("arrUnitData", $arrUnitData);
        //echo 'hello';
        //exit;
        //$this->view->setVar("arrPropertyBillingData", $arrPropertyBillingData);
    }

    /**
     * function to check if outstanding amount is added for given unit
     */
    public function partialwriteoffAction() {
        //echo '<pre>';
        $arrData = $this->request->getPost();
        //$lateCharges = (float)$arrData['lateCharges'];
        $arrResult = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPartialWriteOffData', $arrData); //get all Unit details
        ////print_r($arrNewResult);exit;
        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("arrInvoiceWriteOff", $arrResult);
    }

    /**
     * function to check if outstanding amount is added for given unit
     */
    private function _invoicePaymentTracker($unit_id, $arrData = array()) {
//        echo '<pre>hello';print_r($arrData['arrPostData']);exit;
        $auth = $this->session->get('auth');
        $arrResponse = array('status' => 'error');
        $arrPostData = $arrData['arrPostData'];
        if (!empty($arrPostData)) {
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
            $arrIncomeInvoiceDetail = $arrData['arrIncomeInvoiceDetail'];

            $arrInvoiceDataListener['invoice_detail'] = $arrIncomeInvoiceDetail;
            $arrInvoiceDataListener['postedData'] = $arrPostData;
            $arrReceiptDetail = array('payment_tracker_id' => $arrPostData['payment_tracker_id'], 'receipt_number' => $arrPostData['receipt_number']);
            $oustandingAmountPaid = 0;

            //Getting bank/cash ledger details
            //            $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id'=>$auth['soc_id'])); //get all Unit details
            //            $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail'=>$arrAccountDetail)); //get all Unit details
            //            if(!empty($arrLedgerAccountDetail))
            //            {
            //                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
            //                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
            //                if(strtolower($arrPostData['payment_mode']) != 'cash')
            //                {
            //                    if(!empty($arrPostData['bank_account']))
            //                    {
            //                        $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
            //                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
            //                    }
            //                    else
            //                    {
            //                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
            //                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
            //                    }
            //                }
            //            }
            //            
            //            $strNarration = '';
            //            if(!empty($arrPostData['payment_mode']) && in_array(strtolower($arrPostData['payment_mode']), $this->constants['payment_mode_for_clearance']))
            //            {
            //                $strNarration = ' with transaction ref. ('.$arrPostData['transaction_reference'].', '.$arrPostData['payment_instrument'].')';
            //            }
            //            elseif(!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer')
            //            {
            //                $strNarration = ' with payment ref. ('.$arrPostData['transaction_reference'].')';
            //            }
            //Adavnace payment if no due pending
            if ((empty($arrPostData['total_unpaid_amount']) || $arrPostData['total_unpaid_amount'] <= 0) && empty($arrPostData['member_paid_invoice'])) {
                if ($arrPostData['payment_amount'] > 0) {
                    $countLedgerEntry = 0;
                    //                    $arrgenerateInvoiceid = array('soc_id'=> $auth['soc_id']);
                    //                    $receipt_id = $this->incometrackerevent->incometracker('InvoiceGenerator:generate_receipt_id', $arrgenerateInvoiceid);
                    $arrPostData['soc_id'] = $auth['soc_id'];
                    //                    $arrPostData['receipt_number'] = $receipt_id;
                    if (!empty($arrIncomeInvoiceMemberDetail)) {
                        $arrPostData['unit_id'] = $arrIncomeInvoiceMemberDetail['fk_unit_id'];
                        $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                    }
                    //$arrPostData['unit_invoice_id'] = $arrPostData['invoice_number'] = 0;
                    if (!empty($arrPostData['payment_date']) && strrchr($arrPostData['payment_date'], '-')) {
                        $arrPostData['payment_date'] = $this->getDisplayDate($arrPostData['payment_date']);
                    }
                    $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:paidAdvanceInvoiceAmount', array('arrPostData' => $arrPostData, 'arrReceiptDetail' => $arrReceiptDetail)); //get all Unit details

                    if ($arrResponse['status'] == 'success') {
                        if (ACCOUNT_MODULE_EXIST == 1) {
                            $arrListnerData = array('auth' => $auth, 'unit_id' => $unit_id);
                            $arrUnitDetails = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', $arrListnerData);
                            if (!empty($arrUnitDetails)) {
                                if ($arrUnitDetails['ledger_account_id']) {
                                    $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                                } else {
                                    $arrListnerData['ledger_name'] = 'BLDG#' . strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                                    $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
                                }
                                $arrUnitLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', $arrListnerData);
                                if (!empty($arrUnitLedgerDetails)) {
                                    $countLedgerEntry = $this->incometrackerevent->incometracker('MemberIncomeDetail:paymentLedgerEntry', array('auth' => $auth, 'arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails)); //get all Unit details
                                    //                                    $arrMaintenanceAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getIncomeAccountByName', array('soc_id'=> $auth['soc_id'], 'account_name'=>'MaintenanceFee', 'account_type'=>'member'));
                                    //                                    if(!empty($arrMaintenanceAccountDetail))
                                    //                                    {
                                    //                                        if(!empty($arrIncomeAccounts))
                                    //                                        {
                                    //                                            $arrLedgerTransactionData = array('auth' => $auth);
                                    //                                            $arrLedgerTransactionData['voucher_type'] = '';
                                    //                                            $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
                                    //                                            $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
                                    //                                            $arrLedgerTransactionData['transaction_date'] = $this->getCurrentDate('database');
                                    //                                            $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];
                                    //                                            $arrLedgerTransactionData['narration'] = 'Advance payment received from '.ucwords($arrPostData['received_from']).' dated '.$this->getCurrentDate('display').' through '.$arrPostData['payment_mode'].$strNarration; //$eachInvoiceDetail['invoice_number'] . ' late payment charges';
                                    //                                            $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
                                    //                                            $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
                                    //                                            $arrLedgerTransactionData['payment_reference'] = $arrPostData['transaction_reference'];
                                    //                                            $arrLedgerTransactionData['transaction_type'] = '';
                                    //                                            $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'];
                                    //                                            $arrLedgerTransactionData['other_payment_ref'] = '';
                                    //                                            $arrLedgerEntry = $this->incometrackerevent->incometracker('accountListener:transactionLedgerEntry', $arrLedgerTransactionData);
                                    //                                            if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                                    //                                                $countLedgerEntry++;
                                    //                                            }
                                    //                                        }
                                    //
									//                                    }
                                }
                            }
                        }

                        //Send notification
                        if ($countLedgerEntry == 0) {
                            $arrEmailData['soc_id'] = $auth['soc_id'];
                            $arrEmailData['title'] = 'invoice_payment';
                            $arrEmailData['currency'] = 'Rs.';
                            $arrEmailData['member_name'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
                            $arrEmailData['mobile_number'] = $arrIncomeInvoiceMemberDetail['member_mobile_number'];
                            $arrEmailData['email'] = array($arrEmailData['member_name'] => $arrIncomeInvoiceMemberDetail['member_email_id']);
                            $arrEmailData['priority'] = \ChsOne\Components\Email\Email::PRIORITY_MEDIUM_STORE_IN_DATABASE;
                            $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
                            $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
                            $arrEmailData['society_name'] = ucwords($arrSocietyDetail['soc_name']);
                            $arrEmailData['chsone_site_link'] = $this->config->system->full_base_url_fe;
                            $arrEmailData['chsone_tiny_url'] = CHSONE_TINY_URL;
                            $arrEmailData['total_amount'] = number_format(round($arrPostData['payment_amount'], 2), 2, '.', '');
                            $arrEmailData['payment_date'] = $this->getCurrentDate('display');
                            $arrEmailData['bill_number'] = 'Advance';
                            $emailResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendEmailFromTemplate', $arrEmailData);
                            $smsResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendSmsFromTemplate', $arrEmailData);
                            $arrResponse = array('status' => 'success');
                            $unit_name = strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                            $title = "Invoice paid by $unit_name";
                            $socUsers = $this->session->get("socUsers");
                            $description = 'Payment of ' . $arrEmailData['currency'] . $arrEmailData['total_amount'] . ' received from ' . $unit_name . ' against invoice #' . $arrEmailData['bill_number'] . ' on ' . $arrEmailData['payment_date'] . '.';
                            $notification_data = ['title' => $title, 'desc' => $description, 'scope' => 'user', 'to_be_notified' => 1, 'member_id' => [$arrIncomeInvoiceMemberDetail['id']], 'module' => 'incometracker', 'date_time' => date("d-M-Y H:i"), 'soc_id' => $auth['soc_id'], 'all_staff' => [], 'to_be_notified' => 1, 'all_admin' => $socUsers['socAdminUsers']];
                            \ChsOne\Helper\CommonHelper::addNotification($notification_data);
                        }
                    }
                }
                return $arrResponse;
            } else {
                echo '<pre>';
                $arrInvoicePaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:setInvoicePaymentData', $arrInvoiceDataListener); //get all Unit details
                //get tds deducted payment entries
                if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                    $arrInvoicePaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPaymentDetailsAfterTds', array('arrInvoicePaymentDetail' => $arrInvoicePaymentDetail, 'arrPostData' => $arrPostData)); //get payment details of all settled invoices after tds deduction
                }
                ////print_r($arrInvoicePaymentDetail);
                //                echo 'hello';//exit;
                if (isset($arrInvoicePaymentDetail['outstanding_payment_amount']) && !empty($arrInvoicePaymentDetail['outstanding_payment_amount'])) {
                    $arrOustandingAmountDetail = $arrInvoicePaymentDetail['outstanding_payment_amount'];
                    unset($arrInvoicePaymentDetail['outstanding_payment_amount']);
                }
            }

            //$cashDetail = $this->incometrackerevent->incometracker('IncomeAccount:getcashaccountdetails', array('auth'=>$this->session->get("auth"))); //get all Unit details
            //            //print_r($arrLedgerDetail);  ////print_r($cashDetail);    
            //            exit;
            // echo 'hello1';//print_r($arrInvoicePaymentDetail);exit;
            //            //print_r($arrInvoiceDataListener);
            //            //print_r($arrOustandingAmountDetail);

            if (!empty($arrInvoicePaymentDetail)) {
                //exit;
                if (!empty($arrInvoicePaymentDetail["paidInvoiceLedger"])) {
                    $arrPostData['member_paid_invoice'] = $arrInvoicePaymentDetail["paidInvoiceLedger"];
                    unset($arrInvoicePaymentDetail['paidInvoiceLedger']);
                }
                $arrInvoicePaymentResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveIncomeInvoicePayment', array('paymentDetail' => $arrInvoicePaymentDetail, 'arrReceiptDetail' => $arrReceiptDetail)); //get all Unit details

                if (!empty($arrInvoicePaymentResponse['success']) && count($arrInvoicePaymentDetail) == count($arrInvoicePaymentResponse['success'])) {
                    $countLedgerEntry = 0; //exit;
                    if (ACCOUNT_MODULE_EXIST == 1) {
                        $arrListnerData = array('auth' => $auth, 'unit_id' => $unit_id);
                        $arrUnitDetails = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', $arrListnerData);

                        if ($arrUnitDetails['ledger_account_id']) {
                            $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                        } else {
                            $arrListnerData['ledger_name'] = 'BLDG#' . strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                            $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
                        }
                        $arrUnitLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', $arrListnerData);

                        $countLedgerEntry = $this->incometrackerevent->incometracker('MemberIncomeDetail:paymentLedgerEntry', array('auth' => $auth, 'arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails)); //get all Unit details
                        //ledger entries for late payment charges

                        foreach ($arrInvoicePaymentDetail as $eachInvoiceDetail) {
                            $incomeInvoiceParticular = new IncomeInvoiceParticular();
                            $objQueryBuiler = $incomeInvoiceParticular->updateIncomeInvoiceParticular(array('soc_id' => $auth['soc_id'], 'arrParticularDetail' => $eachInvoiceDetail, 'updated_date' => $this->getCurrentDate('database')));
                        }
                    }
                    //exit;

                    if ($countLedgerEntry == 0) {
                        //send email on payment
                        $arrEmailData['soc_id'] = $auth['soc_id'];
                        $arrEmailData['title'] = 'invoice_payment';
                        $arrEmailData['currency'] = 'Rs.';
                        $arrEmailData['member_name'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
                        $arrEmailData['mobile_number'] = $arrIncomeInvoiceMemberDetail['member_mobile_number'];
                        $arrEmailData['email'] = array($arrEmailData['member_name'] => $arrIncomeInvoiceMemberDetail['member_email_id']);
                        $arrEmailData['priority'] = \ChsOne\Components\Email\Email::PRIORITY_MEDIUM_STORE_IN_DATABASE;
                        $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
                        $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
                        $arrEmailData['society_name'] = ucwords($arrSocietyDetail['soc_name']);
                        $arrEmailData['chsone_site_link'] = $this->config->system->full_base_url_fe;
                        $arrEmailData['chsone_tiny_url'] = CHSONE_TINY_URL;
                        $unit_name = strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                        $title = "Invoice paid by $unit_name";
                        $arrEmailData['bill_number'] = $arrPostData['member_paid_invoice'];
                        $arrEmailData['payment_date'] = $this->getCurrentDate('display');
                        $arrEmailData['total_amount'] = number_format(round($arrPostData['payment_amount'], 2), 2, '.', '');
                        $emailResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendEmailFromTemplate', $arrEmailData);
                        $smsResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendSmsFromTemplate', $arrEmailData);
                        //                         //print_r($arrEmailData);
                        //                         exit;
                        $socUsers = $this->session->get("socUsers");
                        $description = 'Payment of ' . $arrEmailData['currency'] . $arrEmailData['total_amount'] . ' received from ' . $unit_name . ' against invoice #' . $arrEmailData['bill_number'] . ' on ' . $arrEmailData['payment_date'] . '.';
                        $notification_data = ['title' => $title, 'desc' => $description, 'scope' => 'user', 'to_be_notified' => 1, 'member_id' => [$arrIncomeInvoiceMemberDetail['id']], 'module' => 'incometracker', 'date_time' => date("d-M-Y H:i"), 'soc_id' => $auth['soc_id'], 'all_staff' => [], 'to_be_notified' => 1, 'all_admin' => $socUsers['socAdminUsers']];
                        \ChsOne\Helper\CommonHelper::addNotification($notification_data);

                        $arrResponse = array('status' => 'success');
                    }
                    //update income unit invoice
                }
            }
        }
        return $arrResponse;
    }

    /**
     * function to check if outstanding amount is added for given unit
     */
    private function _invoiceWrtieoffPaymentTracker($unit_id, $arrData = array()) {
        //echo '<pre>';//exit;
        $auth = $this->session->get('auth');
        $arrResponse = array('status' => 'error');

        $arrPostData = $arrData['arrPostData'];
        ////print_r($arrPostData);
        if (!empty($arrPostData)) {
            $arrIncomeInvoiceMemberDetail = $arrData['arrIncomeInvoiceMemberDetail'];
            $arrIncomeInvoiceDetail = $arrData['arrIncomeInvoiceDetail'];
            $arrReceiptDetail = array('payment_tracker_id' => $arrPostData['payment_tracker_id'], 'receipt_number' => $arrPostData['receipt_number']);

            $arrInvoiceDataListener['invoice_detail'] = $arrIncomeInvoiceDetail;
            $arrInvoiceDataListener['postedData'] = $arrPostData;
            $arrInvoiceDataListener['postedData']['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
            $arrInvoiceDataListener['postedData']['soc_id'] = $auth['soc_id'];
            $oustandingAmountPaid = 0;
            $arrInvoicePaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:setInvoiceWriteoffPayment', $arrInvoiceDataListener); //get all Unit details
            $arrPaidInvoiceDetail = $arrInvoicePaymentDetail['arrPaidInvoiceDetail'];
            //            echo '<pre>',print_r($arrInvoiceDataListener['postedData']['writeoff_amount']),'</pre>';exit;
            if (!empty($arrPostData['tds_amount']) && $arrPostData['tds_amount'] > 0) {
                $arrPaidInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPaymentDetailsAfterTds', array('arrInvoicePaymentDetail' => $arrPaidInvoiceDetail, 'arrPostData' => $arrPostData)); //get payment details of all settled invoices after tds deduction
            }
            ////print_r($arrPaidInvoiceDetail);//exit;
            //            echo '####################################################################';
            //$arrLedgerDetail = $arrInvoicePaymentDetail['arrLedgerDetail'];
            //Getting bank/cash ledger details
            //            $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id'=>$auth['soc_id'])); //get all Unit details
            //            $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail'=>$arrAccountDetail)); //get all Unit details
            //            $strNarration = '';
            //                    
            //            if(!empty($arrLedgerAccountDetail))
            //            {
            //                $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['cash']['ledger_id'];
            //                $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['cash']['ledger_name'];
            //                if(strtolower($arrPostData['payment_mode']) != 'cash')
            //                {
            //                    if(!empty($arrPostData['bank_account']))
            //                    {
            //                        $arrIncomeAccounts['ledger_id'] = $arrPostData['bank_account'];
            //                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['arrBank'][$arrPostData['bank_account']];
            //                    }
            //                    else
            //                    {
            //                        $arrIncomeAccounts['ledger_id'] = $arrLedgerAccountDetail['bank']['ledger_id'];
            //                        $arrIncomeAccounts['ledger_name'] = $arrLedgerAccountDetail['bank']['ledger_name'];
            //                    }
            //                    
            //                    $strNarration = ' with transaction ref. ('.$arrPostData['transaction_reference'].', '.$arrPostData['payment_instrument'].')';
            //                    if(!empty($arrPostData['payment_mode']) && strtolower($arrPostData['payment_mode']) == 'cashtransfer')
            //                    {
            //                        $strNarration = ' with payment ref. ('.$arrPostData['transaction_reference'].')';
            //                    }
            //                }
            //            }

            if (!empty($arrPaidInvoiceDetail)) {
                //transaction to rollback invoice records in case of failure
                $arrInvoicePaymentResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveIncomeInvoicePayment', array('paymentDetail' => $arrPaidInvoiceDetail, 'arrReceiptDetail' => $arrReceiptDetail)); //get all Unit details
                if (!empty($arrInvoicePaymentResponse['success']) && count($arrPaidInvoiceDetail) == count($arrInvoicePaymentResponse['success'])) {
                    $countLedgerEntry = 0;
                    if (ACCOUNT_MODULE_EXIST == 1) {

                        //echo 'hello1';//print_r($arrInvoicePaymentDetail);
                        $accountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id' => $auth['soc_id'])); //get all Unit details
                        $arrAccountLedgerDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail' => $accountDetail)); //get all Unit details

                        $incomeInvoiceParticular = new IncomeInvoiceParticular();
                        $objQueryBuiler = $incomeInvoiceParticular->updateIncomeInvoiceParticularStatus(array('soc_id' => $auth['soc_id'], 'invoice_number' => $arrPostData['member_paid_invoice'], 'updated_date' => $this->getCurrentDate('database')));

                        $arrListnerData = array('auth' => $auth, 'unit_id' => $unit_id);
                        $arrUnitDetails = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', $arrListnerData);

                        if ($arrUnitDetails['ledger_account_id']) {
                            $arrListnerData['ledger_id'] = $arrUnitDetails['ledger_account_id'];
                        } else {
                            $arrListnerData['ledger_name'] = 'BLDG#' . strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                            $arrListnerData['context'] = ACCOUNT_RECEIVABLE_GROUP;
                        }
                        $arrUnitLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', $arrListnerData);

                        $countLedgerEntry = $this->incometrackerevent->incometracker('MemberIncomeDetail:paymentLedgerEntry', array('auth' => $auth, 'arrPostData' => $arrPostData, 'arrUnitLedgerDetails' => $arrUnitLedgerDetails)); //get all Unit details
                        // exit;   
                        //Ledger entry for payment amount
                        //                        $arrLedgerTransactionData = array('auth' => $auth);
                        //                        $arrLedgerTransactionData['voucher_type'] = '';
                        //                        $arrLedgerTransactionData['from_ledger_id'] = $arrIncomeAccounts['ledger_id'];
                        //                        $arrLedgerTransactionData['to_ledger_id'] = $arrUnitLedgerDetails['recieving_ledger_id'];
                        //                        $arrLedgerTransactionData['transaction_date'] = $this->getCurrentDate('database');
                        //                        $arrLedgerTransactionData['transaction_amount'] = $arrPostData['payment_amount'];
                        //                        $arrLedgerTransactionData['narration'] = 'Amount received against Invoice '.$arrPostData['member_paid_invoice'].' dated '.$this->getCurrentDate('display').' through '.$arrPostData['payment_mode'].$strNarration;//'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                        //                        $arrLedgerTransactionData['from_ledger_name'] = $arrIncomeAccounts['ledger_name'];
                        //                        $arrLedgerTransactionData['to_ledger_name'] = $arrUnitLedgerDetails['receiver_name'];
                        //                        $arrLedgerTransactionData['payment_reference'] = $arrPostData['transaction_reference'];
                        //                        $arrLedgerTransactionData['transaction_type'] = '';
                        //                        $arrLedgerTransactionData['mode_of_payment'] = $arrPostData['payment_mode'];
                        //                        $arrLedgerTransactionData['other_payment_ref'] = '';
                        //                        $arrLedgerEntry = $this->incometrackerevent->incometracker('accountListener:transactionLedgerEntry', $arrLedgerTransactionData);
                        //                        if (isset($arrLedgerE3ntry['error']) && !empty($arrLedgerEntry['error'])) {
                        //                            $countLedgerEntry++;
                        //                        }
                        //                        
                        //                        //Ledger entry for invoice writeoff
                        //                        $arrLedgerTransactionData['transaction_amount'] = $arrPostData['writeoff_amount'];
                        //                        $arrLedgerTransactionData['narration'] = 'Amount writeoff against Invoice '.$arrPostData['member_paid_invoice'].' dated '.$this->getCurrentDate('display');//'Invoice '.$arrPostData['member_paid_invoice'].' '.$eachLedgerDetail['particular'].' Write Off';
                        //                        $arrLedgerEntry = $this->incometrackerevent->incometracker('accountListener:transactionLedgerEntry', $arrLedgerTransactionData);
                        //                        if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                        //                            $countLedgerEntry++;
                        //                        }
                    }
                    //exit;
                    if ($countLedgerEntry == 0) {
                        //send email on payment
                        $arrEmailData['soc_id'] = $auth['soc_id'];
                        $arrEmailData['title'] = 'invoice_payment';
                        $arrEmailData['currency'] = 'Rs.';
                        $arrEmailData['member_name'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
                        $arrEmailData['mobile_number'] = $arrIncomeInvoiceMemberDetail['member_mobile_number'];
                        $arrEmailData['email'] = array($arrEmailData['member_name'] => $arrIncomeInvoiceMemberDetail['member_email_id']);
                        $arrEmailData['priority'] = \ChsOne\Components\Email\Email::PRIORITY_MEDIUM_STORE_IN_DATABASE;
                        $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
                        $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
                        $arrEmailData['society_name'] = ucwords($arrSocietyDetail['soc_name']);
                        $arrEmailData['chsone_site_link'] = $this->config->system->full_base_url_fe;
                        $arrEmailData['chsone_tiny_url'] = CHSONE_TINY_URL;
                        $arrEmailData['bill_number'] = $eachInvoicePaymentdetail['invoice_number'];
                        $arrEmailData['payment_date'] = $this->getCurrentDate('display');

                        $arrEmailData['total_amount'] = number_format(round($arrPostData['payment_amount'], 2), 2, '.', '');
                        $arrEmailData['bill_number'] = $arrPostData['member_paid_invoice'];
                        $arrEmailData['payment_date'] = $this->getCurrentDate('display');

                        $emailResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendEmailFromTemplate', $arrEmailData);
                        $smsResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendSmsFromTemplate', $arrEmailData);
                        $unit_name = strtoupper($arrUnitDetails['soc_building_name']) . '-' . $arrUnitDetails['unit_flat_number'];
                        $title = "Invoice paid by $unit_name";

                        $socUsers = $this->session->get("socUsers");

                        $description = 'Payment of ' . $arrEmailData['currency'] . $arrEmailData['total_amount'] . ' received from ' . $unit_name . ' against invoice #' . $arrEmailData['bill_number'] . ' on ' . $arrEmailData['payment_date'] . ' with write off amount ' . $arrInvoiceDataListener['postedData']['writeoff_amount'] . '.';
                        $notification_data = ['title' => $title, 'desc' => $description, 'scope' => 'group', 'to_be_notified' => 1, 'member_id' => [$arrIncomeInvoiceMemberDetail['id']], 'module' => 'incometracker', 'date_time' => date("d-M-Y H:i"), 'soc_id' => $auth['soc_id'], 'all_staff' => [], 'all_admin' => $socUsers['socAdminUsers']];
                        \ChsOne\Helper\CommonHelper::addNotification($notification_data);
                        // echo '<pre>', print_r($notification_data), '</pre>'; exit;
                        $arrResponse['status'] = 'success';
                    }
                    // update income unit invoice
                }
            }
        }
        return $arrResponse;
    }

    /**
     * function to check if outstanding amount is added for given unit
     */
    public function incomepaymenttrackerAction($payment_tracker_id = '', $action = 0) {
        $auth = $this->session->get('auth');
        
        //Search Filter Start
        $this->setActionUrl('income-details/incomepaymenttracker', 'income-details/incomepaymenttracker');
        $this->setSearchView('income-details/incomepaymenttracker', ["paymentTracker.transaction_reference"=>"cheque_no", 
            "paymentTracker.receipt_number" => "receipt_number",
            "paymentTracker.invoice_number" => "invoice_number",
            "paymentTracker.payment_date"=>"payment_date",
            "paymentTracker.unit_number" => "unit_number",
            "paymentTracker.received_from" => "received_from"
            ]
        );
        $arrGeneralSettingData = array('soc_id' => $auth['soc_id'], 'setting_key' => array('INCOME_PAYMENT_MODE'));
        $arrInvoiceGeneralSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceGeneralSetting', $arrGeneralSettingData);
//        $payment_mode   =   array();
        $payment_mode   =   explode(',',$arrInvoiceGeneralSetting[0]["setting_value"]);
        
        $arrPaymentMode =   array_combine($payment_mode, $payment_mode);
//        print_r($arrPaymentMode); exit;
        $this->setFilterView('income-details/incomepaymenttracker', [
    			"paymentTracker.status"=>["R" => "received", "P" => "submitted", "Y" => "cleared", "N" => "bounced", "not_received" => "not_received", "reversed" => "reversed"],
    			"paymentTracker.payment_mode"=> $arrPaymentMode,
                        "pdc" => ["pdc" => "pdc"]
                ]
        );
        
        if($this->request->isPost()){
            $searchArr  =   $this->request->getPost();
//            print_r($searchArr); exit;
            $searchFilterPost['search_by'] = $searchArr['search_by'];
            $searchFilterPost['search_key'] = $searchArr['search_key'];
            $searchFilterPost['filters'] = $searchArr['filters'];
            $this->setPostSearchFilterArr('income-details/incomepaymenttracker', $searchFilterPost);
            
        }
    	//Search Filter End

        $payment_tracker_id = $this->request->getPost("payment_tracker_id", "striptags");

        $action = $this->request->getPost("action", "striptags");

        if (!empty($payment_tracker_id)) {
            $arrPaymentTrackerListener['searchReceiptTrackerQuery']  = \ChsOne\Helper\SearchHelper::getSearchQuery('income-details/incomepaymenttracker');
            $arrPaymentTrackerListener['filterReceiptTrackerQuery']  = \ChsOne\Helper\SearchHelper::getFilterQuery('income-details/incomepaymenttracker');


            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'P', 'current_date' => $this->getCurrentDate('database'));
            if ($action == 3) {
                $arrPaymentTrackerListener['status'] = 'R';
            } elseif ($action == 2) {
                $arrPaymentTrackerListener['status'] = array('P', 'R');
            } elseif ($action == 4) {
                $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'payment_reversal' => 'y', 'bill_type' => array('member', 'creditaccount-member'));
            }
//            echo '<pre>xcf'; print_r($arrPaymentTrackerListener); exit;
            $arrPostData = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); // get all Unit details
            if (!empty($arrPostData)) {

                $unit_id = $arrPostData['unit_id'];
                $arrPostData['tds_amount'] = $arrPostData['tds_deducted'];
                $arrDataListener['soc_id'] = $auth['soc_id'];
                $arrDataListener['unit_id'] = $unit_id;
                $arrPostData['payment_tracker_id'] = $arrPostData['id'];
                // //print_r($arrPostData);/exit;
                if (!empty($unit_id)) {
                    $arrIncomeInvoiceMemberDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberDetail', $arrDataListener); // get all Unit details
                    if (empty($arrIncomeInvoiceMemberDetail)) {
                        $arrAjaxResonse = array('status' => 'error', 'message' => 'Member not allotted to unit.');
                        $this->session->set("err_rule", $arrAjaxResonse['message']);
                        echo json_encode($arrAjaxResonse);
                        exit();
                    }
                    $arrPostData['member_name'] = $arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name'];
                }

                $this->soc_db_w = $this->di->getShared('soc_db_w');
                $this->soc_db_w->begin();

                $payment_date = $this->request->getPost("payment_date", "striptags");
                if (in_array($action, array(1, 3)) && !empty($payment_date)) {
                    if ($this->getDatabaseDate($payment_date) < $arrPostData['payment_date']) {
                        $arrAjaxResonse = array('status' => 'error', 'message' => 'Cheque clearance date cannot be lesser than Cheque submission date.');
                        $this->session->set("err_rule", $arrAjaxResonse['message']);
                        echo json_encode($arrAjaxResonse);
                        exit();
                    }
                    if ($this->getDatabaseDate($payment_date) > $this->getCurrentDate('database')) {
                        $arrAjaxResonse = array('status' => 'error', 'message' => 'Date cannot be greater than todays date.');
                        $this->session->set("err_rule", $arrAjaxResonse['message']);
                        echo json_encode($arrAjaxResonse);
                        exit();
                    }
                }

                if ($action == 1) {
                    // payment date check
                    if (!empty($arrPostData['payment_date'])) {
                        if (!empty($payment_date)) {
                            $this->incometrackerevent->addListener('incomeGeneralSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
                            $arrGeneralInvoiceSetting = $this->incometrackerevent->incometracker('incomeGeneralSetting:getgeneralsetting', array('soc_id' => $auth['soc_id']));
                            if (!empty($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) && strtolower($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) == 'submission') {
                                $arrPostData['clearance_date'] = $payment_date;
                                $payment_date = $arrPostData['payment_date'];
                            }
                            if (!empty($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) && strtolower($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) == 'clearance') {
                                $payment_date = $this->getDatabaseDate($payment_date);
                                if ($payment_date >= $arrPostData['payment_date']) {
                                    // update income tracker payment date
                                    $payment_date = $this->getDisplayDate($payment_date);
                                }
                            }
                        } else {
                            $payment_date = $arrPostData['payment_date']; // $this->getDisplayDate($arrPostData['payment_date']);
                        }
                        //close account checking for FY
                        $accountingDate = (isset($arrPostData['clearance_date']) && !empty(isset($arrPostData['clearance_date']))) ? $arrPostData['clearance_date'] : $payment_date;
                        $arrClosingAccountDetail = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id' => $auth['soc_id'], 'bill_date' => $accountingDate));
                        if (!empty($arrClosingAccountDetail) && count($arrClosingAccountDetail) > 0) {
                            $arrAjaxResonse = array('status' => 'error', 'message' => 'Accounting has been closed, Cannot make payment.');
                            $this->session->set("err_rule", $arrAjaxResonse['message']);
                            echo json_encode($arrAjaxResonse);
                            exit();
                        }
                    }
                    $arrPostData['payment_date'] = $payment_date;

                    $arrDataListener['soc_id'] = $auth['soc_id'];
                    $arrDataListener['unit_id'] = $unit_id;
                    // $arrDataListener['track_payment'] = 1;
                    if (!empty($arrIncomeInvoiceMemberDetail)) {
                        $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                    }
                    if ($arrPostData['bill_type'] == 'creditaccount-member') {
                        $data = $arrPostData['other_information']['credit_account_detail'];
                        $data['payment_amount'] = $arrPostData['payment_amount'];
                        if ($data['credit_used_type'] == 'refundable') {
                            $arrToLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistOrCreate', array('auth' => array("soc_id" => $this->auth['soc_id']), 'ledger_name' => $data['buildingUnit']));
                            $data['income_account_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
                            $narration = 'Refundable deposit recieved via ' . ucfirst($data['payment_mode']) . '(' . ucfirst($data['narration']) . ').';
                            $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:createMemberLedger', array('auth' => $this->auth, 'unit_id' => $unit_id, 'arrPostData' => $arrPostData['other_information']['credit_account_detail'], 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'to_ledger' => $arrToLedgerDetails, 'narration' => $narration));
                        } else {
                            $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:createMemberLedger', array('auth' => $this->auth, 'unit_id' => $unit_id, 'arrPostData' => $arrPostData['other_information']['credit_account_detail'], 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                        }
                        $data['payment_tracker_id'] = $payment_tracker_id;
                        $data['payment_date'] = $this->getDatabaseDate($payment_date);
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:saveCreditAccountResponse', array('auth' => true, 'process' => 'fetch', 'soc_id' => $this->auth['soc_id'], 'id' => 0, 'data' => $data, 'user' => $this->auth['user_id'], 'username' => $this->auth['user_first_name'] . ' ' . $this->auth['user_last_name']));
                    } elseif ($arrPostData['bill_type'] == 'creditaccount-nonmember') {
                        $data = $arrPostData['other_information']['credit_account_detail'];
                        $data['payment_amount'] = $arrPostData['payment_amount'];
                        if ($data['credit_used_type'] == 'refundable') {
                            $narration = 'Refundable deposit recieved via ' . ucfirst($data['payment_mode']) . '(' . ucfirst($data['narration']) . ').';
                            $arrToLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistOrCreate', array('auth' => array("soc_id" => $this->auth['soc_id']), 'ledger_name' => $data['account_id'] . '-' . $data['account_name']));
                            $data['income_account_ledger_id'] = $arrToLedgerDetails['recieving_ledger_id'];
                            // Insert transaction entry
                            $intBookerLedgerDetails = $this->incometrackerevent->incometracker('nomemberincomelistner:payNonmemberBillLedger', array('auth' => $this->auth, 'postData' => $data, 'arrBookerLedgerDetails' => $arrToLedgerDetails, 'to_ledger' => $arrToLedgerDetails, 'narration' => $narration));
                        } else {
                            $narration = 'Advance payment received from ' . ucfirst($data['account_name']) . ' dated ' . $payment_date . ' through ' . $data['payment_mode'] . ' with transaction ref.(' . $data['transaction_reference'] . ', ' . $data['payment_instrument'] . ')';
                            $arrBookerLedgerDetails['recieving_ledger_id'] = $data['nonmember_ledger_id'];
                            $arrBookerLedgerDetails['receiver_name'] = $data['account_name'];
//                            $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $this->auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
                            $intBookerLedgerDetails = $this->incometrackerevent->incometracker('nomemberincomelistner:payNonmemberBillLedger', array('auth' => $auth, 'postData' => $arrPostData['other_information']['credit_account_detail'], 'arrBookerLedgerDetails' => $arrBookerLedgerDetails, 'narration' => $narration));
                        }
                        if (!empty($intBookerLedgerDetails)) {
                            $arrResponse['status'] = 'success';
                        }
                        $data['payment_tracker_id'] = $payment_tracker_id;
                        $data['payment_date'] = $this->getDatabaseDate($payment_date);
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:saveCreditAccountResponse', array('auth' => true, 'process' => 'fetch', 'soc_id' => $this->auth['soc_id'], 'id' => 0, 'data' => $data, 'user' => $this->auth['user_id'], 'username' => $this->auth['user_first_name'] . ' ' . $this->auth['user_last_name']));
                    } elseif (strtolower($arrPostData['bill_type']) == 'member') {
                        if (!empty($arrPostData['other_information']['member_detail']['bank_ledger'])) {
                            $arrPostData['bank_account'] = $arrPostData['other_information']['member_detail']['bank_ledger'];
                        }

                        if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitUnpaidInvoiceByInvoiceNumber', array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'invoice_number' => $arrPostData['member_paid_invoice'])); // get all Unit details
                        } else {
                            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceUnpaidBill', $arrDataListener); // get all Unit details
                            if (!empty($arrIncomeInvoiceDetail)) {
                                $memberPaidInvoices = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberPaidInvoices', array('payment_amount' => $arrPostData['payment_amount'], 'unpaidInvoiceDetail' => $arrIncomeInvoiceDetail));
                                if (!empty($memberPaidInvoices)) {
                                    $arrPostData['member_paid_invoice'] = trim(implode(',', $memberPaidInvoices), ',');
                                }
                                $arrPostData['total_unpaid_amount'] = $arrIncomeInvoiceDetail['total_unpaid_invoice_amount'];
                            } else {
                                $arrPostData['member_paid_invoice'] = '';
                                $arrPostData['total_unpaid_amount'] = 0;
                            }
                        }

                        $arrPaymentTrackerListener = array('arrPostData' => $arrPostData, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail, 'arrIncomeInvoiceDetail' => $arrIncomeInvoiceDetail);
                        if (!empty($arrPostData['writeoff_amount']) && $arrPostData['writeoff_amount'] > 0) {
                            $arrResponse = $this->_invoiceWrtieoffPaymentTracker($unit_id, $arrPaymentTrackerListener);
                        } else {
                            $arrResponse = $this->_invoicePaymentTracker($unit_id, $arrPaymentTrackerListener);
                        }
                    } elseif (strtolower($arrPostData['bill_type']) == 'common_bill') {
                        $arrCommonListenerData['auth'] = $auth;
                        if (!empty($arrPostData['other_information'])) {
                            if (!empty($arrPostData['other_information']['common_bill_detail']) && !empty($arrPostData['other_information']['common_bill_detail']['payment_type']) && strtolower($arrPostData['other_information']['common_bill_detail']['payment_type']) == 'quickpay') {
                                // For TDS adjustment
                                if (!empty($arrPostData['tds_amount'])) {
                                    $arrPostData['payment_amount'] += $arrPostData['tds_amount'];
                                }

                                $unpaidInvoiceDetail = $this->incometrackerevent->incometracker('commonBilling:getIncidentInvoiceUnpaidBill', $arrDataListener);
                                $arrIncidentPaymentInvoices = $this->incometrackerevent->incometracker('commonBilling:getIncidentPaymentInvoices', array('postData' => $arrPostData, 'arrUnpaidInvoices' => $unpaidInvoiceDetail));
                                $arrCommonBillDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', array('auth' => $auth, 'unit_id' => $unit_id));

                                $arrResponse = $this->incometrackerevent->incometracker('commonBilling:saveMultiCommonBillPayments', array('auth' => $auth, 'postData' => $arrPostData, 'arrUnpaidInvoicesDetail' => $arrIncidentPaymentInvoices['invoice_payment_detail'])); // get all Unit details
                                if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                                    $arrPostData['member_paid_invoice'] = $arrPostData['invoice_number'] = $arrIncidentPaymentInvoices['member_paid_invoice'];
                                    // if(!empty($arrCommonBillResponse) && $arrCommonBillResponse['success'] == 1)
                                    {
                                        $arrUnitDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', array('auth' => $auth, 'unit_id' => $unit_id));
                                        $arrPostData['bank_account'] = $arrPostData['other_information']['common_bill_detail']['bank_ledger'];
                                        $arrPostData['bill_type_category'] = $arrPostData['other_information']['common_bill_detail']['bill_type_category'];
                                        $arrPostData['tds_amount'] = $arrPostData['tds_deducted'];
                                        // //print_r($arrPostData);//exit;
                                        $countLedgerEntry = $this->incometrackerevent->incometracker('commonBilling:payCommonBillLedger', array('auth' => $auth, 'arrPostData' => $arrPostData, 'arrCommonBillDetail' => $arrUnitDetail)); // get all Unit details
                                        // echo $countLedgerEntry;
                                        if ($countLedgerEntry == 0) {
                                            $arrResponse['status'] = 'success';
                                        }
                                    }
                                }
                                // echo 'END';exit;
                                //print_r($arrResponse);exit;
                                // //print_r($arrPostData);exit;
                            } elseif (!empty($arrPostData['other_information']['common_bill_detail']) && !empty($arrPostData['other_information']['common_bill_detail']['common_bill_id'])) {

                                // $arrPostData['member_id'] = $arrIncomeInvoiceMemberDetail['fk_member_id'];
                                $arrPostData['common_bill_id'] = $arrPostData['other_information']['common_bill_detail']['common_bill_id'];
                                $arrCommonListenerData = array('auth' => $auth, 'id' => $arrPostData['common_bill_id']);

                                $arrCommonBillDetail = $this->incometrackerevent->incometracker('commonBilling:getCommonBillById', array('auth' => $auth, 'id' => $arrPostData['common_bill_id'])); // get all Unit details
                                if (!empty($arrCommonBillDetail)) {
                                    $arrCommonBillDetail = current($arrCommonBillDetail);

                                    $arrCommonBillPaymentDetails = $this->incometrackerevent->incometracker('commonBilling:setCommonBillPayments', array('auth' => $auth, 'arrUnpaidInvoices' => $arrCommonBillDetail, 'arrPostData' => $arrPostData)); // get all Unit details
                                    if (!empty($arrCommonBillPaymentDetails['arrPostDetail'])) {
                                        $arrPostData = $arrCommonBillPaymentDetails['arrPostDetail'];
                                    }

                                    $arrCommonListenerData['PostData']['common_bill_id'] = $arrPostData['other_information']['common_bill_detail']['common_bill_id'];
                                    $arrCommonListenerData['PostData']['unit_id'] = $arrPostData['unit_id'];
                                    $arrCommonListenerData['PostData']['member_id'] = $arrDataListener['member_id'];
                                    $arrCommonListenerData['PostData']['invoice_number'] = $arrCommonBillDetail['invoice_number'];
                                    $arrCommonListenerData['PostData']['payment_mode'] = $arrPostData['payment_mode'];
                                    $arrCommonListenerData['PostData']['transaction_reference'] = $arrPostData['transaction_reference'];
                                    $arrCommonListenerData['PostData']['payment_instrument'] = $arrPostData['payment_instrument'];
                                    $arrCommonListenerData['PostData']['payment_amount'] = $arrPostData['payment_amount'];
                                    $arrCommonListenerData['PostData']['received_from'] = $arrPostData['received_from'];
                                    $arrCommonListenerData['PostData']['bank_account'] = $arrPostData['other_information']['common_bill_detail']['bank_ledger'];
                                    $arrCommonListenerData['PostData']['billing_type'] = $arrCommonBillDetail['billing_type'];
                                    $arrCommonListenerData['PostData']['payment_status'] = $arrCommonBillDetail['payment_status'];
                                    $arrCommonListenerData['PostData']['bill_type_category'] = $arrPostData['other_information']['common_bill_detail']['bill_type_category'];
                                    $arrCommonListenerData['PostData']['tds_amount'] = $arrPostData['tds_deducted'];
                                    $arrCommonListenerData['PostData']['payment_status'] = $arrPostData['payment_status'];
                                    $arrCommonListenerData['PostData']['payment_date'] = $arrPostData['payment_date'];
                                    $arrCommonListenerData['PostData']['payment_note'] = $arrPostData['payment_note'];
                                    $arrCommonListenerData['PostData']['clearance_date'] = $arrPostData['clearance_date'];
                                    $arrCommonListenerData['PostData']['member_name'] = $arrPostData['member_name'];

                                    if (isset($arrPostData['paid_advance_amount'])) {
                                        $arrCommonListenerData['PostData']['paid_advance_amount'] = $arrPostData['paid_advance_amount'];
                                    }

                                    $arrCommonBillResponse = $this->incometrackerevent->incometracker('commonBilling:saveCommonBillPayment', $arrCommonListenerData); // get all Unit details
                                    if (!empty($arrCommonBillResponse) && $arrCommonBillResponse['success'] == 1) {
                                        $countLedgerEntry = $this->incometrackerevent->incometracker('commonBilling:payCommonBillLedger', array('auth' => $auth, 'arrPostData' => $arrCommonListenerData['PostData'], 'arrCommonBillDetail' => $arrCommonBillDetail)); // get all Unit details
                                        if ($countLedgerEntry == 0) {
                                            $arrResponse['status'] = 'success';
                                            $arrPostData['member_paid_invoice'] = $arrCommonListenerData['PostData']['invoice_number'];
                                        }
                                    }
                                }
                            }
                        }
                    } elseif (strtolower($arrPostData['bill_type']) == 'nonmember') {
                        $arrNonmemberIncomeDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberIncomeDetail', array('soc_id' => $auth['soc_id'], 'bill_number' => $arrPostData['member_paid_invoice']));
                        if (!empty($arrPostData['other_information']['nonmember_detail']['bank_ledger'])) {
                            $arrPostData['bank_account'] = $arrPostData['other_information']['nonmember_detail']['bank_ledger'];
                        }
                        if (!empty($arrNonmemberIncomeDetail)) {
                            $arrPostData['payment_type'] = 'receipt';
                            if (!empty($arrNonmemberIncomeDetail)) {
                                $arrPaymentDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberPaymentDetail', array('soc_id' => $auth['soc_id'], 'nonmember_bill_id' => $arrNonmemberIncomeDetail['nonmember_bill_id']));
                                if (!empty($arrPaymentDetail)) {
                                    $totalPaidAmount = (float) $arrPaymentDetail['payment_amount'] + $arrPaymentDetail['tds_deducted'];
                                }
                            }
                            // $totalPaidAmount = $this->incometrackerevent->incometracker('nomemberincomelistner:getTotalIncomePaymentDetail', array('soc_id'=>$auth['soc_id'], 'bill_number'=>$arrPostData['member_paid_invoice']));
                            $total_due_amount = (float) round(($arrNonmemberIncomeDetail['bill_amount'] + $arrNonmemberIncomeDetail['total_taxes']) - ($arrNonmemberIncomeDetail['total_deduction'] + $arrNonmemberIncomeDetail['discount_amount'] + $totalPaidAmount), 3);
                            $payment_status = 'partialpaid';
                            if ($arrPostData['payment_amount'] >= $total_due_amount) {
                                $payment_status = 'paid';
                            }

                            $arrListnerPaymentData['invoice_details'] = $arrPostData;
                            $arrListnerPaymentData['invoice_details']['bill_number'] = $arrNonmemberIncomeDetail['bill_number'];
                            $arrListnerPaymentData['auth'] = $this->session->get('auth');
                            $arrListnerPaymentData['nonmember_invoice_id'] = $arrNonmemberIncomeDetail['nonmember_bill_id'];
                            $addmemberincomepayment = $this->incometrackerevent->incometracker('nomemberincomelistner:addadvancepayment', $arrListnerPaymentData);

                            if ($addmemberincomepayment['success'] == true) {
                                // Update nonmember bill status
                                $updateIncomeStatus = $this->incometrackerevent->incometracker('nomemberincomelistner:updateNonmemberIncomeStatus', array('soc_id' => $auth['soc_id'], 'status' => $payment_status, 'bill_number' => $arrNonmemberIncomeDetail['bill_number']));
                                if (!empty($updateIncomeStatus) && strtolower($updateIncomeStatus['status'] == 'success')) {

                                    //Get nonmember ledger
                                    $arrNonmemberMasterDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberDetail', array('soc_id' => $auth['soc_id'], 'nonmember_id' => $arrNonmemberIncomeDetail['nonmember_id']));
                                    if (!empty($arrNonmemberMasterDetail['nonmember_ledger_id'])) {
                                        $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $auth, 'ledger_id' => $arrNonmemberMasterDetail['nonmember_ledger_id']));
                                    } else {
                                        $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
                                    }

                                    // Get income account ledger
                                    // $arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:getIncomeAccountLedgerDetails', array('auth'=>$auth, 'nonmemberincomeaccount'=>$arrNonmemberIncomeDetail['fk_income_account_id']));
                                    // Insert transaction entry
                                    $intBookerLedgerDetails = $this->incometrackerevent->incometracker('nomemberincomelistner:payNonmemberBillLedger', array('auth' => $auth, 'postData' => $arrPostData, 'arrBookerLedgerDetails' => $arrBookerLedgerDetails));
                                    if (!empty($intBookerLedgerDetails)) {
                                        $arrResponse['status'] = 'success';
                                        $arrPostData['member_paid_invoice'] = $arrNonmemberIncomeDetail['bill_number'];
                                    }
                                }
                            }
                        }
                    }

                    // //print_r($arrResponse);exit;
                    if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                        $arrUpdatePaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'invoice_number' => $arrPostData['member_paid_invoice']);
                        if (!empty($payment_date)) {
                            if (strrchr($payment_date, '/')) {
                                $arrUpdatePaymentTrackerListener['payment_date'] = $this->getDatabaseDate($payment_date);
                            } else {
                                $arrUpdatePaymentTrackerListener['payment_date'] = $payment_date;
                            }
                        }
                        $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', $arrUpdatePaymentTrackerListener); // get all Unit details
                    }
                    $succMsg = (strtolower($arrPostData['payment_mode']) == 'cheque') ? "Cheque payment has cleared successfully." : "Payment has cleared successfully.";
                } elseif ($action == 2) {
                    if (strtolower($arrPostData['status']) == 'r') {
                        $arrEmailData['title'] = 'cheque_payment_not_received';
                        $arrUpdatePaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'not_received');
                    } else {
                        $arrEmailData['title'] = 'cheque_payment_bounced';
                        $arrUpdatePaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'N');
                    }
                    $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', $arrUpdatePaymentTrackerListener); // get all Unit details
                    if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                        if (strtolower($arrPostData['bill_type']) == 'member') {
                            $arrUpdateUnitInvoiceListener = array('soc_id' => $auth['soc_id'], 'invoice_number' => $arrPostData['member_paid_invoice'], 'status' => 'generated');
                            $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:updateUnitInvoiceStatus', $arrUpdateUnitInvoiceListener); // get all Unit details
                        } elseif (strtolower($arrPostData['bill_type']) == 'nonmember') {
                            if (isset($arrPostData['other_information']['nonmember_detail']['advance_payment']) && !empty($arrPostData['other_information']['nonmember_detail']['advance_payment'])) {
                                $arrUpdateUnitInvoiceListener = array('soc_id' => $auth['soc_id'], 'bill_number' => $arrPostData['member_paid_invoice'], 'status' => 'unpaid', 'advance_amount' => 0);
                                $arrResponse = $this->incometrackerevent->incometracker('nomemberincomelistner:updateNonmemberIncomeAdavanceStatus', $arrUpdateUnitInvoiceListener); // get all Unit details
                            }
                            $arrNonmemberIncomeDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberIncomeDetail', array('soc_id' => $auth['soc_id'], 'bill_number' => $arrPostData['member_paid_invoice']));
                            if (!empty($arrNonmemberIncomeDetail)) {
                                $memberName = explode(' ', $arrNonmemberIncomeDetail['billed_name']);
                                $arrIncomeInvoiceMemberDetail['member_first_name'] = current($memberName);
                                $arrIncomeInvoiceMemberDetail['member_last_name'] = trim(str_replace($arrIncomeInvoiceMemberDetail['member_first_name'], '', $arrNonmemberIncomeDetail['billed_name']));
                                $arrIncomeInvoiceMemberDetail['member_mobile_number'] = $arrNonmemberIncomeDetail['booker_mobile_number'];
                                $arrIncomeInvoiceMemberDetail['member_email_id'] = $arrNonmemberIncomeDetail['booker_email_address'];
                            }
                        }
                    }

                    $succMsg = "Payment has not received.";

                    // Send Notification on cheque bounce
                    $arrEmailData['soc_id'] = $auth['soc_id'];
                    $arrEmailData['currency'] = 'Rs.';
                    $paymentDate = explode(' ', $arrPostData['created_date']);

                    $arrEmailData['payment_date'] = (!empty($paymentDate[0]) && strrchr($paymentDate[0], '-')) ? $this->getDisplayDate($paymentDate[0]) : $paymentDate[0];
                    $arrEmailData['cheque_number'] = $arrPostData['transaction_reference'];
                    $arrEmailData['bank_name'] = $arrPostData['payment_instrument'];
                    $arrEmailData['total_amount'] = ($arrPostData['writeoff_amount'] > 0) ? number_format(round($arrPostData['writeoff_amount'], 2), 2, '.', '') : number_format(round($arrPostData['payment_amount'], 2), 2, '.', '');
                    $arrEmailData['member_name'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
                    $arrEmailData['mobile_number'] = $arrIncomeInvoiceMemberDetail['member_mobile_number'];
                    $arrEmailData['email'] = array($arrEmailData['member_name'] => $arrIncomeInvoiceMemberDetail['member_email_id']);
                    $arrEmailData['priority'] = \ChsOne\Components\Email\Email::PRIORITY_MEDIUM_STORE_IN_DATABASE;
                    $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
                    $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); // get all Unit details
                    $arrEmailData['society_name'] = ucwords($arrSocietyDetail['soc_name']);
                    $arrEmailData['chsone_site_link'] = $this->config->system->full_base_url_fe;
                    $arrEmailData['chsone_tiny_url'] = CHSONE_TINY_URL;
                    $emailResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendEmailFromTemplate', $arrEmailData);
                    $smsResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendSmsFromTemplate', $arrEmailData);
                    // //print_r($emailResponse);//print_r($smsResponse);
                    // exit;
                } elseif ($action == 3) { // Update pdc to submitted state
                    $arrUpdatePaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'P');
                    // Add/Update bank account
                    $arrPostData['other_information'][$arrPostData['bill_type'] . '_detail']['bank_ledger'] = $this->request->getPost("bank_account", "striptags");
                    $arrUpdatePaymentTrackerListener['other_information'] = serialize($arrPostData['other_information']);

                    if (!empty($payment_date)) {
                        $arrUpdatePaymentTrackerListener['payment_date'] = $this->getDatabaseDate($payment_date);
                    }
                    $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', $arrUpdatePaymentTrackerListener); // get all Unit details
                    $succMsg = "Cheque payment has submitted successfully.";
                } elseif ($action == 4) {
                    $arrPostData['reversal_note'] = $this->request->getPost("reversal_note", "striptags");
                    if (empty($arrPostData['reversal_note'])) {
                        $arrAjaxResonse = array('status' => 'error', 'message' => 'Reversal note has not given.');
                        $this->session->set("err_rule", $arrAjaxResonse['message']);
                        echo json_encode($arrAjaxResonse);
                        exit();
                    }
                    $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:paymentReversalProcess', array('auth' => $auth, 'arrPostData' => $arrPostData, 'arrMemberDetail' => $arrIncomeInvoiceMemberDetail)); // get all Unit details
                    $succMsg = $arrResponse['message'];
                }

                if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                    // exit;
                    $this->soc_db_w->commit(); // commit all records
                    $arrAjaxResonse = array('status' => 'success', 'message' => $succMsg);
                    $this->session->set("succ_msg", $succMsg);
                    // return $this->response->redirect($this->config->system->full_base_url . "income-details/incomepaymenttracker?page=".$number_page);
                } else {
                    $this->soc_db_w->rollback(); // rollback in case of failure
                    $arrAjaxResonse = array('status' => 'error', 'message' => 'Unable to complete transaction, Please try later.');
                    $this->session->set("err_rule", 'Unbale to complete transaction, Please try later.');
                }
            } else {
                $arrAjaxResonse = array('status' => 'error', 'message' => 'Unable to complete transaction, No invoice found.');
                $this->session->set("err_rule", 'Unable to complete transaction, Payment already proccessed.');
            }
            echo json_encode($arrAjaxResonse);
            exit();
        }

        $searchData = array();
        $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'full_list' => false);


        
        $theme = $_SESSION['theme'];
         /** For Code CleanUp against search and filter (Harshada S :- 11/01/2019) Start **/  
        if ($theme == 'classic') {

            // search filter start
            $searchFilterQuery = [];
            $filters = [];
            $searchFilterQuery = $this->request->getQuery();
            if (isset($searchFilterQuery) && !empty($searchFilterQuery['filter'])) {
                foreach ($searchFilterQuery['filter'] as $key => $val) {
                    $filters[] = $key;
                }
                $searchFilterQuery['filters'] = $filters;
            }

            $searchFilterQuery['action_url'] = 'income-details/incomepaymenttracker';
            $searchFilterQuery['module'] = 'incomes';
            $searchFilterQuery['entity'] = 'receipt_tracker';
            //$searchFilterQuery['action_url'] = $config->system->full_base_url.'vendorbill/vendorBill';
            $searchFilterQuery['search_by_items'] = ["unit_no", "member_name", "cheque_no", "receipt_no", "invoice_no", "payment_date_search"];
            $searchFilterQuery['filter_by_items'] = [
                "payment_status" => ["received", "submitted", "cleared", "bounced", "not_received", "reversed"],
                "payment_mode" => ["cheque", "cash_transfer", "cash", "online"],
                "pdc" => ["pdc"]
            ];
            $this->setSearchFilterData($searchFilterQuery);

            $searchFilterParams = $this->session->get('search_filter')['incomes']['receipt_tracker'];

            $this->view->setVar("search_params", $_SESSION['search_filter']['incomes']['receipt_tracker']);
            // search filter end
        }


        $searchQuery = $this->request->getQuery();
        unset($searchQuery['_url']);
        unset($searchQuery['apply']);
        $pageNum = 1;
        if ($this->session->has('search_income_tracker')) {

            $arr = [];
            if (isset($_SESSION['search_income_tracker']['search'])) {
                $arr = $_SESSION['search_income_tracker']['search'];
            }

            $srQuery = $searchQuery;
            unset($arr['page']);
            unset($srQuery['page']);
            $searchData = array_replace($arr, $srQuery);

            $arrPaymentTrackerListener['searchData'] = $searchData;
        } else {

            if (isset($searchQuery['search_by']) || isset($searchQuery['search_all_date']) || isset($searchQuery['search_all']) ||
                    isset($searchQuery['filter_by'])) {
                $arrPaymentTrackerListener['searchData'] = $searchQuery;
            }

            if (isset($searchQuery['total_pdc']) && !empty($searchQuery['total_pdc'])) {
                $searchQuery['payment_status'] = array('r');
            }
            $this->session->set("search_income_tracker", array('search' => $arrPaymentTrackerListener['searchData']));
        }
        
         /** For Code CleanUp against search and filter (Harshada S :- 11/01/2019) End **/  

//        echo '<pre>'; print_r($arrPaymentTrackerListener); exit;
        $arrPaymentTrackerListener['searchReceiptTrackerQuery']  = \ChsOne\Helper\SearchHelper::getSearchQuery('income-details/incomepaymenttracker');
        $arrPaymentTrackerListener['filterReceiptTrackerQuery']  = \ChsOne\Helper\SearchHelper::getFilterQuery('income-details/incomepaymenttracker');
        $objQueryBuiler = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerList', $arrPaymentTrackerListener); // get all Unit details

        $arrQuery = $objQueryBuiler->getQuery()->execute()->toArray();

        $totalPage = ((count($arrQuery)) / PAGE_NUMBER);
        $httpRefferal = explode("?", $_SERVER['HTTP_REFERER'])[0];
        $actualUrl = explode("?", ($this->config->system->full_base_url . "income-details/incomepaymenttracker"))[0];

        $search_filter  =   $this->session->get('search_filter');
        
        if (isset($this->session->get('search_income_tracker')['page']) && isset($searchQuery['page'])) {
            $pageNum = $searchQuery['page'];
        } elseif (isset($this->session->get('search_income_tracker')['page']) && empty($searchQuery['page'])) {
            if ($httpRefferal == $actualUrl) {
                if (empty($searchQuery['search_by']) || empty($searchQuery['search_all_date']) || empty($searchQuery['search_all']) ||
                        empty($searchQuery['filter_by'])) {
                    $pageNum = 1;
                } else {
                    $pageNum = $this->session->get('search_income_tracker')['page'];
                }
            } else {
                $pageNum = $this->session->get('search_income_tracker')['page'];
            }
        } else
        if (isset($searchQuery['page']) && ($searchQuery['page'] != '' || $searchQuery['page'] > 0)) {
            $pageNum = $searchQuery['page'];
        } else{
            $pageNum = $search_filter['income-details/incomepaymenttracker']['current_page'];
        }
        $number_page = $pageNum;

        
        $paginator = new Paginatorbyquery(array(
            "builder" => $objQueryBuiler,
            "limit" => PAGE_NUMBER,
            "page" => $pageNum
        ));
        $page = $paginator->getPaginate();

//        if (isset($this->session->get('search_income_tracker')['page']) && isset($searchQuery['page'])) {
//            $pg = $searchQuery['page'];
//        } elseif (isset($this->session->get('search_income_tracker')['page']) && empty($searchQuery['page'])) {
//            if ($httpRefferal == $actualUrl) {
//                if (!isset($searchQuery['search_by']) || !isset($searchQuery['search_all_date']) || !isset($searchQuery['search_all']) ||
//                        !isset($searchQuery['filter_by'])) {
//                    $pg = 1;
//                } else {
//                    $pg = $this->session->get('search_income_tracker')['page'];
//                }
//            } else {
//                $pg = $this->session->get('search_income_tracker')['page'];
//            }
//        } elseif (isset($searchQuery['page'])) {
//            $pg = $searchQuery['page'];
//        } else
//            $pg = $number_page;
//
//        $pg = $number_page;
//        $this->session->set("search_income_tracker", array('page' => $pg, 'search' => $arrPaymentTrackerListener['searchData']));

        $arrPaymentTrackerDetail = $page->items->toArray();

        // get formated listing data of tracker
        $arrInvoicePaymentTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPaymentTrackerListedData', array('arrPaymentTrackerDetail' => $arrPaymentTrackerDetail)); //get all Unit details
        //Get total pdc count in IncomeTracker
        $arrInvoicePaymentTrackerPaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getIncomeTrackerDetailByStatus', array('soc_id' => $auth['soc_id'], 'status' => 'r')); //get all Unit details by payment status

        if (isset($_SESSION['search_income_tracker']['page'])) {
            $this->view->setVar("current_page", $this->session->get('search_income_tracker')['page']);
        } else {
            $this->view->setVar("current_page", $pg);
        }
        // $this->view->setVar("current_page",  $pg);
        $total_pdc = 0;
        if (!empty($arrInvoicePaymentTrackerPaymentDetail)) {
            $total_pdc = count($arrInvoicePaymentTrackerPaymentDetail);
        }
        unset($arrPaymentTrackerListener['searchData']['page']);
        unset($arrPaymentTrackerListener['searchData']['apply']);
        $this->view->setVar('paymentReversalDate', $this->getDisplayDate(ACTIVE_PAYMENT_REVERSAL_DATE));

        $this->view->setVar('search_arr', $arrPaymentTrackerListener['searchData']);
        $this->view->setVar('search', (http_build_query($arrPaymentTrackerListener['searchData']) . "\n"));
        $this->view->setVar("page", $page);
        $this->view->setVar("number_page", $number_page);
        $this->view->setVar("page_url", ((http_build_query('/admin/income-details/incomepaymenttracker?' . $arrPaymentTrackerListener['searchData']) . "\n")));
        $this->view->setVar("arrInvoicePaymentTracker", $arrInvoicePaymentTracker);
        $this->view->setVar("total_pdc", $total_pdc);
        $this->view->setLayout("admin");
        $this->view->setVar("config", $this->config);
    }

    /**
     * Update cheque payment
     * @param string $paymentTrackerId
     * @return unknown
     */
    public function updatePaymentTrackerAction($paymentTrackerId = '') {
        $options = array();
        $err_msgs = [];

        $auth = $this->session->get("auth");
        $page_from = $this->request->getQuery("from");
        if (isset($paymentTrackerId) && !empty($paymentTrackerId)) {
            $this->view->setVar('payment_tracker_id', $paymentTrackerId);
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $paymentTrackerId, 'status' => array('P', 'R'));

            $arrPaymentTrackerData = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); // get all Unit details
            //echo '<pre>';print_r($arrPaymentTrackerData);exit;
            $options['payment_tracker_data'] = $arrPaymentTrackerData;
            $options['form_mode'] = 'edit';
            $redirectUrl = $this->config->system->full_base_url . "income-details/incomepaymenttracker";
            if(!empty($page_from) && strtolower($page_from)=='invoicelist')
            {
                $redirectUrl = $this->config->system->full_base_url . "income-details/memberReceiptslist/".$arrPaymentTrackerData['unit_id'];
            }
            // For TDS adjustment with payment amount
            if (!empty($arrPaymentTrackerData['tds_deducted']) && $arrPaymentTrackerData['tds_deducted'] > 0) {
                $options['payment_tracker_data']['payment_amount'] = (float) $arrPaymentTrackerData['payment_amount'] - $arrPaymentTrackerData['tds_deducted'];
            }

            switch ($arrPaymentTrackerData['bill_type']) {
                case 'member': {
                        $bankLedgId = !empty($arrPaymentTrackerData['other_information']['member_detail']['bank_ledger']) ? $arrPaymentTrackerData['other_information']['member_detail']['bank_ledger'] : 0;
                        break;
                    }
                case 'nonmember': {
                        $bankLedgId = !empty($arrPaymentTrackerData['other_information']['nonmember_detail']['bank_ledger']) ? $arrPaymentTrackerData['other_information']['nonmember_detail']['bank_ledger'] : 0;
                        break;
                    }
                case 'creditaccount-member': {
                        $bankLedgId = !empty($arrPaymentTrackerData['other_information']['nonmember_detail']['bank_ledger']) ? $arrPaymentTrackerData['other_information']['nonmember_detail']['bank_ledger'] : 0;
                        break;
                    }
                case 'creditaccount-nonmember': {
                        $bankLedgId = !empty($arrPaymentTrackerData['other_information']['creditaccount-nonmember']['bank_ledger']) ? $arrPaymentTrackerData['other_information']['creditaccount-nonmember']['bank_ledger'] : 0;
                        break;
                    }
                case 'common_bill': {
                        $bankLedgId = !empty($arrPaymentTrackerData['other_information']['common_bill_detail']['bank_ledger']) ? $arrPaymentTrackerData['other_information']['common_bill_detail']['bank_ledger'] : 0;
                        break;
                    }
                default: {
                        $bankLedgId = 0;
                        break;
                    }
            }
            $options['bank_ledger_id'] = $bankLedgId;

            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $arrPaymentTrackerData['unit_id'];
            $arrIncomeInvoiceMemberDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberDetail', $arrDataListener); // get all Unit details

            if (!empty($arrIncomeInvoiceMemberDetail)) {
                $arrDataListener['invoice_number'] = $arrPaymentTrackerData['member_paid_invoice'];
                $arrDataListener['member_id'] = $arrIncomeInvoiceMemberDetail['id'];
                $options['received_from'] = ucwords($arrIncomeInvoiceMemberDetail['member_first_name'] . ' ' . $arrIncomeInvoiceMemberDetail['member_last_name']);
            }
            $arrUnitDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', array('auth' => $auth, 'unit_id' => $arrDataListener['unit_id'])); // get all Unit details
            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceUnpaidBill', $arrDataListener); // get all Unit details
            $options['billpaymentmode'] = $this->constants['billpaymentmode'];

            $options['bill_type'] = 'member';

            $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id' => $auth['soc_id'])); // get all Unit details
            $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail' => $arrAccountDetail)); // get all Unit details

            $showBankAccount = 0;
            if (!empty($arrLedgerAccountDetail['arrBank'])) {
                $options['bank_account'] = $arrLedgerAccountDetail['arrBank'];
                $options['bank_selected'] = $arrLedgerAccountDetail['bank']['ledger_id'];
                if (count($arrLedgerAccountDetail['arrBank']) > 1) {
                    $showBankAccount = 1;
                }
            }
        }

        $form = new UpdatePaymentTrackerForm($this, $options);

        if ($this->request->isPost()) {

            $data = $this->request->getPost();

            if ($form->isValid($data) == true) {
                // For TDS adjustment
                if (!empty($data['tds_amount']) && $data['tds_amount'] > 0) {
                    $data['payment_amount'] = (float) $data['payment_amount'] + $data['tds_amount'];
                }
                $arrUpdatePaymentTrackerRes = $this->incometrackerevent->incometracker('MemberIncomeDetail:updateChequeInvoicePaymentTracker', ['arrPostData' => $data, 'oldTrackerData' => $arrPaymentTrackerData, 'auth' => $this->auth]); // update cheque payment tracker
                if (isset($arrUpdatePaymentTrackerRes['status']) && $arrUpdatePaymentTrackerRes['status'] == 'success') {
                    $this->session->set('succ_update_cheque_payment', 'Cheque Payment Updated Successfully.');

                    return $this->response->redirect($redirectUrl);
                } else
                    $this->session->set('err_update_cheque_payment', 'Unable to Update Cheque Payment.');
            } else {
                // collect form error messages
                foreach ($form->getMessages() as $message) {
                    $err_msgs[$message->getField()] = (string) $message;
                } // end of foreach form getMessages
            }
        }
        //echo '<pre>';print_r($form);exit;
        $this->view->setVar('err_msg', $err_msgs);
        $this->view->setVar('redirectUrl', $redirectUrl);
        $this->view->setVar("options", $options);
        $this->view->setVar("form", $form);
        $this->view->setLayout("admin");
        $this->view->setVar("config", $this->config);
    }

    /**
     * function to check if outstanding amount is added for given unit
     */
    public function recorddepositAction() {
        //echo '<pre>';
        $auth = $this->session->get('auth');
        $number_page = $this->request->getQuery("page", "int");
        $number_page = ($number_page > 0) ? $number_page : 1;

        $this->incometrackerevent->addListener('incomeaccount', 'ChsOne\Components\IncomeTracker\Setting\Listeners\IncomeAccountListener');
        $options['arrbankAccount'] = $this->incometrackerevent->incometracker('incomeaccount:getbankaccountdetails', array('auth' => $this->session->get("auth")));
        ////print_r($options['arrbankAccount']);exit;
        $options["from_expense_ledger"] = $this->groupledg->getLedgGroupTree(EXPENSES_MODULE, MODE_FROM, ENTITY_TYPE_LEDGER);
        $options["from_income_ledger"] = $this->groupledg->getLedgGroupTree(ASSET, MODE_FROM, ENTITY_TYPE_LEDGER);
        $form = new \ChsOne\IncomeTracker\Forms\RecordDepositForm(NULL, $options);

        if ($this->request->isPost()) {
            $arrPostData = $this->request->getPost();
            if (!empty($arrPostData['member_paid_invoice'])) {
                if ($form->isValid($this->request->getPost()) != false) {
                    $errorFlag = 1;
                    $arrPostListener = array('soc_id' => $auth['soc_id'], 'postData' => $arrPostData);
                    $arrPostRecordDeposit = $this->incometrackerevent->incometracker('MemberIncomeDetail:setRecordDepositPostData', $arrPostListener); //get all Unit details
                    if (!empty($arrPostRecordDeposit) && !empty($arrPostRecordDeposit['from_ledger_id']) && !empty($arrPostRecordDeposit['to_ledger_id'])) {
                        $errorFlag = 0;
                        if (ACCOUNT_MODULE_EXIST == 1) {
                            $toIncomeAccountLedger = array('auth' => $auth, 'ledger_id' => $id);
                            $arrToIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', $toIncomeAccountLedger);
                            foreach ($arrPostRecordDeposit['from'] as $eachLedgerDetail) {
                                if (!empty($eachLedgerDetail['amount']) && $eachLedgerDetail['amount'] > 0) {
                                    $fromIncomeAccountLedger = array('auth' => $auth, 'ledger_id' => $eachLedgerDetail['legder_id']);
                                    $arrFromIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', $fromIncomeAccountLedger);

                                    $arrLedgerTransactionData = array();
                                    $arrLedgerTransactionData = array('auth' => $auth);
                                    $arrLedgerTransactionData['voucher_type'] = '';
                                    if (strtolower($eachLedgerDetail['type']) == 'additional_amount') {
                                        $arrLedgerTransactionData['from_ledger_id'] = $arrToIncomeAccounts['recieving_ledger_id'];
                                        $arrLedgerTransactionData['to_ledger_id'] = $arrFromIncomeAccounts['recieving_ledger_id'];
                                        $arrLedgerTransactionData['from_ledger_name'] = $arrToIncomeAccounts['receiver_name'];
                                        $arrLedgerTransactionData['to_ledger_name'] = $arrFromIncomeAccounts['receiver_name'];
                                    } else {
                                        $arrLedgerTransactionData['from_ledger_id'] = $arrFromIncomeAccounts['recieving_ledger_id'];
                                        $arrLedgerTransactionData['to_ledger_id'] = $arrToIncomeAccounts['recieving_ledger_id'];
                                        $arrLedgerTransactionData['from_ledger_name'] = $arrFromIncomeAccounts['receiver_name'];
                                        $arrLedgerTransactionData['to_ledger_name'] = $arrToIncomeAccounts['receiver_name'];
                                    }

                                    $arrLedgerTransactionData['transaction_date'] = $this->getCurrentDate('database');
                                    $arrLedgerTransactionData['transaction_amount'] = $eachLedgerDetail['amount'];
                                    $arrLedgerTransactionData['narration'] = $eachLedgerDetail['type'] . '  charges against invoices (' . $arrPostData['member_paid_invoice'] . ')'; //$arrPostData['member_paid_invoice'] .' '.$eachLedgerDetail['type']. ' record deposit';

                                    $arrLedgerTransactionData['payment_reference'] = '';
                                    $arrLedgerTransactionData['transaction_type'] = '';
                                    $arrLedgerTransactionData['mode_of_payment'] = '';
                                    $arrLedgerTransactionData['other_payment_ref'] = '';
                                    $arrLedgerEntry = $this->incometrackerevent->incometracker('accountListener:transactionLedgerEntry', $arrLedgerTransactionData);
                                    if (isset($arrLedgerEntry['error']) && !empty($arrLedgerEntry['error'])) {
                                        $errorFlag++;
                                    }
                                }
                            }
                        }
                    }
                    if ($errorFlag > 0) {
                        $this->session->set("err_rule", 'Unbale to complete action, Please try later.');
                    } else {
                        $this->session->set("succ_msg", 'Record deposit is completed successfully');
                    }
                }
            }
        }

        $arrSearchPostData = $this->request->getPost();
        //echo '<pre>';//print_r($arrSearchPostData);exit;
        if (empty($arrSearchPostData['payment_mode'])) {
            $arrSearchPostData['payment_mode'] = 'cheque';
        }
        $arrRecordDepositeListener = array('soc_id' => $auth['soc_id'], 'from_date' => $arrSearchPostData['from_date'], 'to_date' => $arrSearchPostData['to_date'], 'payment_mode' => $arrSearchPostData['payment_mode'], 'full_list' => false);
        $objQueryBuiler = $this->incometrackerevent->incometracker('MemberIncomeDetail:getAllPaidInvoicesDetail', $arrRecordDepositeListener); //get all Unit details

        $paginator = new Paginatorbyquery(array("builder" => $objQueryBuiler, "limit" => PAGE_NUMBER, "page" => $number_page));

        $page = $paginator->getPaginate();
        $arrRecordDepositeDetail = $page->items->toArray();
        $arrAllRecordDepositDetail = array();
        if (!empty($arrRecordDepositeDetail)) {
            $i = 0;
            foreach ($arrRecordDepositeDetail as $eachRecordDepositeDetail) {
                $arrAllRecordDepositDetail[$i] = $eachRecordDepositeDetail;
                $createdDate = explode(' ', $eachRecordDepositeDetail['created_date']);
                $arrAllRecordDepositDetail[$i]['created_date'] = $this->getDisplayDate($createdDate[0]);
                $i++;
            }
        }

        $this->view->setVar("page", $page);
        $this->view->setVar("arrRecordDepositeDetail", $arrAllRecordDepositDetail);
        $this->view->setVar("form", $form);
        $this->view->setLayout("admin");
        $this->view->setVar("config", $this->config);
    }

    /**
     * Pre-action view to be open in pop-up to raise a issue (opens after click on raise issue button)
     * 
     * @method raise_issueAction
     * @access public
     */
    public function quickPayAction($type = 1) {
        $auth = $this->session->get('auth');

        $this->incometrackerevent->addListener('generalSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
        $generalsettingparameter = $this->incometrackerevent->incometracker('generalSetting:getgeneralsetting', $arrListenerdata = array('auth' => $auth));
        $arroptions['generalsetting'] = $generalsettingparameter;

        $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id' => $auth['soc_id'])); //get all Unit details
        $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail' => $arrAccountDetail)); //get all Unit details
        $showBankAccount = 0;
        if (!empty($arrLedgerAccountDetail['arrBank'])) {
            $arroptions['bank_account'] = $arrLedgerAccountDetail['arrBank'];
            $arroptions['bank_selected'] = $arrLedgerAccountDetail['bank']['ledger_id'];
            if (count($arrLedgerAccountDetail['arrBank']) > 1) {
                $showBankAccount = 1;
            }
        }

        if ($type == 1) {
            $actionUrl = 'income-details/quickBillPayment/';
            $incomeType = 'maintenance';
        } elseif ($type == 2) {
            $actionUrl = 'common-billing/quickBillPayment/';
            $incomeType = 'incident';
        }
        $number_page = $this->request->getQuery("page", "int");
        $number_page = ($number_page > 0) ? $number_page : 1;
        $callFrom = $this->request->getQuery("from");
        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("showBankAccount", $showBankAccount);
        $this->view->setVar("actionUrl", $actionUrl);
        $this->view->setVar("page", $number_page);
        $this->view->setVar("callFrom", trim($callFrom));
        $this->view->setVar("incomeType", $incomeType);
        $this->view->setVar("form", new \ChsOne\IncomeTracker\Forms\QuickBillPayForm(null, $arroptions));
        //$this->view->setVar("form", new \ChsOne\HelpDesk\Forms\IssueAddForm());
    }

    /**
     * Returns json encoded string of Members/ Tenants mentioning the unit they reside in.
     * 
     * @method getMembersAction
     * @access public
     */
    public function getMembersAction() {
        $this->view->disable();
        $request = $this->request;
        $posted_values = $this->request->getPost('q');
        if ($request->isPost()) {
            $arrListenerdata = ['auth' => $this->auth, 'posted_values' => ['searchdata' => $posted_values[0], 'member_or_tenant' => $posted_values[1], 'date' => $posted_values[2], 'type' => $posted_values[3]]];

            $list = array();
            if (!empty($arrListenerdata['posted_values']['type'])) {
                $result = $this->incometrackerevent->incometracker('MemberIncomeDetail:searchMembers', $arrListenerdata); //get all Unit details
                $auth = $this->session->get('auth');
                $arrDataListener['soc_id'] = $auth['soc_id'];
                //echo '<pre>';//print_r($result);exit;
                $arrGenerateInvoiceDetail = array();
                if (isset($result['data']) && !empty($result['data'])) {
                    foreach ($result['data'] as $rdr => $data) {
                        $arrDataListener['unit_id'] = $data['unit_id'];
                        $arrDataListener['soc_id'] = $arrListenerdata['auth']['soc_id'];

                        $msg = 'No Pending Due.';
                        $advanceAllowed = 1;

                        if (strtolower($arrListenerdata['posted_values']['type']) == 'maintenance') {
                            $unpaidInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceUnpaidBill', $arrDataListener);
                            if (empty($unpaidInvoiceDetail) && count($unpaidInvoiceDetail) < 1) {
                                $arrAllUnitInvoices = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoices', $arrDataListener);
                                if (empty($arrAllUnitInvoices) && count($arrAllUnitInvoices) < 1) {
                                    $msg = 'Invoice not generated.';
                                    $advanceAllowed = 0;
                                } else {
                                    foreach ($arrAllUnitInvoices as $eachInvoiceDetail) {
                                        if (strtolower($eachInvoiceDetail['status']) == 'in_process') {
                                            $msg = 'Cheque Submitted, Payment In Process.';
                                            $advanceAllowed = 1;
                                            break;
                                        }
                                    }
                                }
                            }
                        } elseif (strtolower($arrListenerdata['posted_values']['type']) == 'incident') {
                            $unpaidInvoiceDetail = $this->incometrackerevent->incometracker('commonBilling:getIncidentInvoiceUnpaidBill', $arrDataListener);
                        } elseif (strtolower($arrListenerdata['posted_values']['type']) == 'generatemaintenance') {
                            $arrDataListener = array('soc_id' => $auth['soc_id']);
                            $arrInvoiceSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceSetting', array('soc_id' => $auth['soc_id'])); //get all Unit details
                            //echo 'hello';//print_r($arrInvoiceSetting);//exit();
                            $frequency = 1;
                            $soc_id = $auth['soc_id'];

                            if (!empty($arrInvoiceSetting)) {
                                switch (strtolower($arrInvoiceSetting['invoicing_frequency'])) {
                                    case 'quarterly':
                                        $frequency = 3;
                                        break;
                                    case 'half_yearly':
                                        $frequency = 6;
                                        break;
                                    case 'yearly':
                                        $frequency = 12;
                                        break;
                                    case 'monthly':
                                    default:
                                        $frequency = 1;
                                        break;
                                }
                            }
                            //echo $frequency;
                            //$form = new GenerateManualBillForm(NULL, array());
                            $date = $this->getCurrentDate('display');
                            $arrGetInvoiceData = array('soc_id' => $auth['soc_id'], 'unit_id' => $data['unit_id'], 'order_by' => 'unit_invoice_id desc');
                            //        echo '<pre>';//print_r($arrGetInvoiceData);exit();
                            $arrInvoiceData = $this->incometrackerevent->incometracker('InvoiceGenerator:getLastUnitInvoiceByUnit', $arrGetInvoiceData);
                            //echo '<pre>';//print_r($arrInvoiceData);//exit();
                            if (!empty($arrInvoiceData)) {
                                $date = (new \DateTime($arrInvoiceData['to_date']))->modify('first day of next month');
                                $date = $date->format('Y-m-d');
                                $date = $this->getDisplayDate($date);

                                if (!empty($arrInvoiceData['from_date'])) {
                                    $arrInvoiceData['from_date'] = $this->getDisplayDate($arrInvoiceData['from_date']);
                                }
                                if (!empty($arrInvoiceData['to_date'])) {
                                    $nextInvoiceStartDate = (new \DateTime($arrInvoiceData['to_date']))->modify('+1 day');
                                    $nextInvoiceStartDate = $nextInvoiceStartDate->format('Y-m-d');
                                    $nextInvoiceStartDate = $this->getDisplayDate($nextInvoiceStartDate);

                                    $arrInvoiceData['to_date'] = $this->getDisplayDate($arrInvoiceData['to_date']);
                                }
                            } elseif (!empty($arrInvoiceSetting['effective_date'])) {
                                $advanceAllowed = $outstandingAdded = 0;
                                $arrInvoiceOutstanding = $this->incometrackerevent->incometracker('InvoiceGenerator:getPendingOutstanding', array('soc_id' => $auth['soc_id'], 'unit_id' => $data['unit_id'], 'bill_type' => 'maintenance'));
                                if (count($arrInvoiceOutstanding) > 0) {
                                    $outstandingAdded = 1;
                                }
                                $nextInvoiceStartDate = $this->getDisplayDate($arrInvoiceSetting['effective_date']);
                            }
                            //echo $nextInvoiceStartDate;exit;

                            $arrGenerateInvoiceDetail = [//'arrInvoiceSetting' => $arrInvoiceSetting,
                                'frequency' => $frequency, 'default_invoice_date' => $nextInvoiceStartDate, 'last_invoice_from_date' => (!empty($arrInvoiceData['from_date'])) ? $arrInvoiceData['from_date'] : '', 'last_invoice_to_date' => (!empty($arrInvoiceData['to_date'])) ? $arrInvoiceData['to_date'] : '', 'outstandingAdded' => $outstandingAdded];
                        }
                        //                    echo json_encode($list);
                        //        exit();
                        $list[$data['id']] = ['id' => $data['id'], 'member' => $data['member'], 'value' => $data['member'], 'name' => ucwords($data['member_name']), 'building' => ucwords($data['soc_building_name']), 'flat' => ucwords($data['unit_flat_number']), 'unit_id' => $data['unit_id'], 'total_due' => (!empty($unpaidInvoiceDetail['total_unpaid_invoice_amount'])) ? number_format(round($unpaidInvoiceDetail['total_unpaid_invoice_amount'], 2), 2, '.', '') : '', 'unpaidInvoices' => (!empty($unpaidInvoiceDetail)) ? $unpaidInvoiceDetail : array(), 'message' => $msg, 'advance_allowed' => $advanceAllowed];
                        //Add key of generateMaintenance
                        if (!empty($arrGenerateInvoiceDetail)) {
                            $list[$data['id']]['frequency'] = $arrGenerateInvoiceDetail['frequency'];
                            $list[$data['id']]['default_invoice_date'] = $arrGenerateInvoiceDetail['default_invoice_date'];
                            $list[$data['id']]['last_invoice_from_date'] = $arrGenerateInvoiceDetail['last_invoice_from_date'];
                            $list[$data['id']]['last_invoice_to_date'] = $arrGenerateInvoiceDetail['last_invoice_to_date'];
                            $list[$data['id']]['outstanding_added'] = $arrGenerateInvoiceDetail['outstandingAdded'];
                        }
                        $list[$data['id']]['arrLastPeriodPaymentTransaction'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPreviousPaymentTransaction', array('soc_id' => $auth['soc_id'], 'unit_id' => $data['unit_id'], 'bill_type' => array('member', 'common_bill', 'common_bill_quickpay', 'creditaccount-member'), 'limit' => 3));
                        //print_r($list);exit;
                    } //end of foreach
                }
            }

            echo json_encode($list);
            exit();
        }
    }

    /**
     * Pre-action view to be open in pop-up to raise a issue (opens after click on raise issue button)
     * 
     * @method raise_issueAction
     * @access public
     */
    public function quickBillPaymentAction() {
        echo '<pre>';
        if ($this->request->isPost()) {
            $posted_values = $this->request->getPost();
            $auth = $this->session->get('auth');
            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $posted_values['unit_id'] = $posted_values['unit_key'];
            $posted_values['bill_type'] = 'member';
            if (!empty($posted_values['id'])) {
                $arrDataListener['member_id'] = $posted_values['id'];
            }
            $unpaidInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceUnpaidBill', $arrDataListener);
            $memberPaidInvoices = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberPaidInvoices', array('payment_amount' => $posted_values['payment_amount'], 'unpaidInvoiceDetail' => $unpaidInvoiceDetail));

            $arrIncomeInvoiceMemberDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberDetail', $arrDataListener); //get all Unit details

            if (!empty($unpaidInvoiceDetail['total_unpaid_invoice_amount'])) {
                $posted_values['total_unpaid_amount'] = $unpaidInvoiceDetail['total_unpaid_invoice_amount'];
            }

            if (!empty($memberPaidInvoices)) {
                $posted_values['member_paid_invoice'] = implode(',', $memberPaidInvoices);
            }

            //For TDS adjustment
            if (!empty($posted_values['tds_amount']) && $posted_values['tds_amount'] > 0) {
                $posted_values['payment_amount'] = (float) $posted_values['payment_amount'] + $posted_values['tds_amount'];
            }
            
            //Set redirection url of quick pay
            switch (strtolower($posted_values['callFrom'])) {
                    case 'invoicedue':
//                        $redirectTo = 'incomemember?page='.$posted_values['page'];
                        $redirectTo = 'incomemember';
                        break;
                    case 'invoicelist':
                        $redirectTo = 'memberReceiptslist/'.$arrDataListener['unit_id'];
                        break;
                    case label3:
                    default:
                        $redirectTo = 'incomemember';
            }
                
            $form = new \ChsOne\IncomeTracker\Forms\QuickBillPayForm(null);

            //Do not create bill if financial years accounting closed    
            $posted_values['arrCloseAccountPayment'] = $this->incometrackerevent->incometracker('accountListener:getClosedAccountDetailByDate', array('soc_id' => $auth['soc_id'], 'bill_date' => $posted_values['payment_date']));

            if ($form->isValid($posted_values) != false) {
                //transaction in case of faliure revert all table entries
                if (!empty($posted_values['member_paid_invoice'])) {
                    $posted_values['member_paid_invoice'] = trim($posted_values['member_paid_invoice'], ',');
                }
                ////print_r($posted_values);exit;
                //Generate receipt number
                $posted_values['receipt_number'] = $this->incometrackerevent->incometracker('InvoiceGenerator:generate_receipt_id', array('soc_id' => $auth['soc_id']));
                //echo '<pre>'.$unit_id;//print_r($posted_values);exit;
                $arrResponseTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:saveInvoicePaymentTracker', array('soc_id' => $auth['soc_id'], 'unit_id' => $posted_values['unit_id'], 'postData' => $posted_values)); //get all Unit details
                $posted_values['payment_tracker_id'] = $arrResponseTracker['payment_tracker_id'];
                $posted_values['soc_id'] = $auth['soc_id'];

                $paymentToken = $this->incometrackerevent->incometracker('MemberIncomeDetail:generatePaymentToken', array('arrPaymentTracker' => $posted_values)); //get all Unit details
                $isTokenValid = $this->incometrackerevent->incometracker('MemberIncomeDetail:paymentTokenVerification', array('paymentToken' => $paymentToken)); //get all Unit details

                $this->soc_db_w = $this->di->getShared('soc_db_w');
                $this->soc_db_w->begin();
                
                if ($isTokenValid) {
                    if (in_array(strtolower($posted_values['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                        $arrResponse = $arrResponseTracker;
                    } else {
                        if (!empty($arrResponseTracker) && strtolower($arrResponseTracker['status']) == 'success') {
                            $arrResponse = $this->_invoicePaymentTracker($arrDataListener['unit_id'], array('arrPostData' => $posted_values, 'arrIncomeInvoiceDetail' => $unpaidInvoiceDetail, 'arrIncomeInvoiceMemberDetail' => $arrIncomeInvoiceMemberDetail));
                        }
                    }
                } else {
                    if (!empty($arrResponseTracker['payment_tracker_id'])) {
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
                    }

                    $this->soc_db_w->commit(); //commit all records
                    $this->session->set("err_rule", 'Unable to complete transaction, Please try later.');
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/".$redirectTo);
                }

                //redirect after response
                if (!empty($arrResponse) && strtolower($arrResponse['status']) == 'success') {
                    $trackertStatus = 'Y';
                    if (in_array(strtolower($posted_values['payment_mode']), $this->constants['payment_mode_for_clearance'])) {
                        $trackertStatus = 'P';
                        if (strtolower($posted_values['payment_mode']) == 'cheque' && $this->getDatabaseDate($posted_values['payment_date']) > $this->getCurrentDate('database')) {
                            $trackertStatus = 'R';
                        }
                    }
                    if (!empty($arrResponseTracker['payment_tracker_id'])) {
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => $trackertStatus, 'transaction_status' => 'complete'));
                    }

                    $this->soc_db_w->commit(); //commit all records
                    //$succMsg = ucwords($arrUnitDetail['soc_building_name']).'/'.ucwords($arrUnitDetail['unit_flat_number']).' '.VENDORBILLPAYMENT_SUCC_MSG;
                    $this->session->set("succ_msg", $unpaidInvoiceDetail[0]['soc_building_name'].' / '. $unpaidInvoiceDetail[0]['unit_name'].' receipt has added successfully');
                } else {
                    $this->soc_db_w->rollback(); //rollback in case of failure
                    $this->session->set("err_rule", 'Unbale to complete transaction, Please try later.');
                }
                // if transaction failed then update the status of payment tracker
                if (empty($arrResponse) || (!empty($arrResponse) && strtolower($arrResponse['status']) != 'success')) {
                    if (!empty($arrResponseTracker['payment_tracker_id'])) {
                        $this->incometrackerevent->incometracker('MemberIncomeDetail:updateIncomePaymentTrackerStatus', array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $arrResponseTracker['payment_tracker_id'], 'status' => 'N', 'transaction_status' => 'complete'));
                    }
                }
            } else {
                //collect form error messages
                foreach ($form->getMessages() as $message) {
                    $err_msgs[$message->getField()] = (string) $message;
                } // end of foreach form getMessages
                ////print_r($err_msgs);exit;
                //$this->session->set("err_rule", 'Unbale to complete transaction, Please try later.');
                $this->session->set("err_msgs", $err_msgs);
            }
        }

        return $this->response->redirect($this->config->system->full_base_url . "income-details/".$redirectTo);
    }

    public function paymentReceiptAction($payment_tracker_id, $soc_id = '', $receipt_type = 'original') {
        if (!empty($soc_id)) { //if soc_id is sent as get parameter
            $auth = ['soc_id' => $soc_id];
            $conce = $this->calMultiDbFlow($auth['soc_id']);
            $this->di->setShared('dbSoc', $conce);
            $this->view->pick('incomedetails/paymentReceiptView');
        } else {
            $auth = $this->session->get('auth');
        }

        if (!empty($auth)) {
            ////print_r($auth);exit;
            $arrReceiptType = array(0=>'Original');
            if (!empty($receipt_type) && strtolower($receipt_type) == 'duplicate') {
                    $arrReceiptType = array('0' => 'Original', '1' => 'Duplicate');
            }
            $this->view->setVar("arrReceiptType", $arrReceiptType);
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id);
            $arrPaymentTrackerData = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); //get all Unit details
            //echo '<pre>';//print_r($arrPaymentTrackerData);//exit;
            if (!empty($arrPaymentTrackerData)) {
                if (!empty($arrPaymentTrackerData['unit_id'])) {
                    $arrUnitDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', array('auth' => $auth, 'unit_id' => $arrPaymentTrackerData['unit_id'])); //get all Unit details
                    $this->view->setVar("unitName", 'of ' . ucwords($arrUnitDetail['soc_building_name'] . '/' . $arrUnitDetail['unit_flat_number']));
                }
            }
            //echo '<pre>';
            $arrGeneralSettingData = array('soc_id' => $auth['soc_id'], 'setting_key' => array('RECEIPT_LOGO'));
            $arrInvoiceGeneralSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceGeneralSetting', $arrGeneralSettingData); //get all Unit details
            ////print_r($arrInvoiceGeneralSetting);exit;
            if (!empty($arrInvoiceGeneralSetting)) {
                foreach ($arrInvoiceGeneralSetting as $key => $eachGeneralSetting) {
                    if (strtolower($eachGeneralSetting['setting_key']) == 'receipt_logo' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                        $arrSocietyWebsiteWelcome = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyWebsiteWelcome', array('soc_id' => $auth['soc_id']));
                        if (!empty($arrSocietyWebsiteWelcome['soc_header_logo'])) {
                            $this->view->setVar("imageLogoUrl", $this->config->s3server_details->s3_security_protocol . $this->config->s3server_details->bucket_name . '.s3.amazonaws.com/socweb/' . $auth['soc_id'] . '/sitelogo/' . $arrSocietyWebsiteWelcome['soc_header_logo']);
                        }
                    }
                }
            }
            //            echo '<pre>';//print_r($arrUnitDetail);exit;
            $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
            $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
            $rupeesInWord = $this->incometrackerevent->incometracker('MemberIncomeDetail:numberToWordRupees', array('number' => $arrPaymentTrackerData['payment_amount']));
            //echo '<pre>';//print_r($arrPaymentTrackerData);exit;
            $arrPaymentTrackerData['adminName'] = ucwords($auth['user_first_name'] . ' ' . $auth['user_last_name']);
            $this->view->setVar("arrSocietyDetail", $arrSocietyDetail);
            $this->view->setVar("arrPaymentTrackerData", $arrPaymentTrackerData);
            $this->view->setVar("rupeesInWords", $rupeesInWord);
            $this->view->setVar("adminName", ucwords($auth['user_first_name'] . ' ' . $auth['user_last_name']));
        }

        $this->view->setVar("config", $this->config);
        $this->view->setVar("type", 'print');
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setLayout("printInvoice");
    }

    public function paymentReceiptViewAction($payment_tracker_id, $soc_id = '') {
        //        //print_r($this->session->get('auth'));exit();
        //            echo $soc_id;exit();
        $invoice_details = explode('/', $this->dncAction($payment_tracker_id));
        //            //print_r($invoice_details);exit();
        $payment_tracker_id = $invoice_details[1];
        $soc_id = $invoice_details[2];
        //           echo '<br>';echo $soc_id;exit();
        $download = $invoice_details[3];
        $timestamp = (!empty($invoice_details[4]) ? $invoice_details[4] : time());
        if (time() > $timestamp) {
            $this->session->set('succ_msg', 'Link has been expired. Try again after some time');
            if (!empty($this->session->get('auth'))) {
                return $this->response->redirect($this->config->system->full_base_url . "income-details/memberReceiptslist/");
            } else {
                return $this->response->redirect($this->config->system->full_base_url . "users/linkExpired");
            }
        }
        $auth = ['soc_id' => $soc_id];
        $conce = $this->calMultiDbFlow($auth['soc_id']);
        $this->di->setShared('dbSoc', $conce);

        if (!empty($auth)) {
            // Rajeshwar - 28-12-2018 -start
            $receipt_type = $this->request->get("receipt_type");
            $this->view->setVar("arrReceiptType", $arrReceiptType);
            //end
            ////print_r($auth);exit;
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id);
            $arrPaymentTrackerData = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); //get all Unit details
            //echo '<pre>';//print_r($arrPaymentTrackerData);//exit;
            if (!empty($arrPaymentTrackerData)) {
                if (!empty($arrPaymentTrackerData['unit_id'])) {
                    $arrUnitDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitDetailById', array('auth' => $auth, 'unit_id' => $arrPaymentTrackerData['unit_id'])); //get all Unit details
                    $this->view->setVar("unitName", 'of ' . ucwords($arrUnitDetail['soc_building_name'] . '/' . $arrUnitDetail['unit_flat_number']));
                }
            }
            //echo '<pre>';//print_r($arrUnitDetail);exit;
            $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
            $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
            $rupeesInWord = $this->incometrackerevent->incometracker('MemberIncomeDetail:numberToWordRupees', array('number' => $arrPaymentTrackerData['payment_amount']));
            $arrPaymentTrackerData['adminName'] = ucwords($auth['user_first_name'] . ' ' . $auth['user_last_name']);
            $this->view->setVar("arrSocietyDetail", $arrSocietyDetail);
            $this->view->setVar("arrPaymentTrackerData", $arrPaymentTrackerData);
            $this->view->setVar("rupeesInWords", $rupeesInWord);
            //$this->view->setVar("adminName", ucwords($auth['user_first_name'].' '.$auth['user_last_name']));
        }
        //        //print_r($arrPaymentTrackerData);exit();
        if (!empty($invoice_details[3]) == 'downloadview') {
            //            //print_r($arrPaymentTrackerData);exit();
            $arrListenerdata['format'] = 'pdf';
            $data['arrPaymentTrackerData'] = $arrPaymentTrackerData;
            $data['arrSocietyDetail'] = $arrSocietyDetail;
            $data['rupeesInWord'] = $rupeesInWord;
            $data['receipt_type'] = $receipt_type;
            $data['adminName'] = ucwords($auth['user_first_name'] . ' ' . $auth['user_last_name']);
            $this->view->setVar("adminName", ucwords($auth['user_first_name'] . ' ' . $auth['user_last_name']));
            $result = $this->invoiceexport->exportDocument('InvoiceExport:receiptDownload', $data);
        }
        //        $this->view->setLayout("printInvoice");
        $this->view->setVar("type", 'print');
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setVar("config", $this->config);
    }

    public function downloadReceiptAction() {
        echo "<script>
        window.close();
        </script>";
        exit();
    }

    /**
     * Pre-action view to be open in pop-up for payment tracker action confirmation (opens after click on tracker action button)
     * 
     * @method paymentTrackerConfirmationAction
     * @access public
     */
    public function paymentTrackerConfirmationAction($payment_tracker_id, $action, $page) {
        $page = ($page > 0) ? $page : 1;
        $showPaymentDate = 0;
        $auth = $this->session->get('auth');
        $this->incometrackerevent->addListener('incomeGeneralSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');

        $arrGeneralInvoiceSetting = $this->incometrackerevent->incometracker('incomeGeneralSetting:getgeneralsetting', array('soc_id' => $auth['soc_id']));

        if ((!empty($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']) && in_array(strtolower($arrGeneralInvoiceSetting['Late_Charges_Calculation_From']), array('clearance', 'submission')) && $action == 1)) {

            $showPaymentDate = 1;
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'P', 'current_date' => $this->getCurrentDate('database'));
            $arrPaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); //get received payment detail
        } elseif ($action == 3) {

            $showPaymentDate = 1;
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'R', 'current_date' => $this->getCurrentDate('database'));
            $arrPaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); //get received payment detail

            if (!empty($arrPaymentDetail['other_information'][$arrPaymentDetail['bill_type'] . '_detail']['bank_ledger'])) {
                $arrPaymentDetail['bank_account'] = $arrPaymentDetail['other_information'][$arrPaymentDetail['bill_type'] . '_detail']['bank_ledger'];
            }
            $arrAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getBankCashAccountDetail', array('soc_id' => $auth['soc_id'])); //get all Unit details
            $arrLedgerAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLedgerAccountDetail', array('account_detail' => $arrAccountDetail)); //get all Unit details
            //            echo '<pre>';
            //            //print_r($arrPaymentDetail['bank_account']);//print_r($arrPaymentDetail);//print_r($arrLedgerAccountDetail);exit;
            //$showBankAccount = 0;
            if (!empty($arrLedgerAccountDetail['arrBank'])) {
                $arrBankDetail['bank_account'] = $arrLedgerAccountDetail['arrBank'];
                $arrBankDetail['bank_selected'] = !empty($arrPaymentDetail['bank_account']) ? $arrPaymentDetail['bank_account'] : $arrLedgerAccountDetail['bank']['ledger_id'];
                $this->view->setVar("arrBankDetail", $arrBankDetail);
            }
        } elseif ($action == 2) {

            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id);
            $arrPaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); //get received payment detail
        } elseif ($action == 4) {

            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'payment_tracker_id' => $payment_tracker_id, 'status' => 'Y', 'payment_reversal' => 'y', 'bill_type' => array('member', 'creditaccount-member'));
            $arrPaymentDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerDetail', $arrPaymentTrackerListener); //get received payment detail
        }
        if (in_array($action, array(1, 3)) && !empty($arrPaymentDetail['payment_date'])) {

            $this->view->setVar("paymentDate", $arrPaymentDetail['payment_date']);
        }

        //                    echo '<pre>';
        //            //print_r($arrBankDetail);exit;
        //echo $payment_tracker_id.'||'.$action;exit;
        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("payment_tracker_id", $payment_tracker_id);
        $this->view->setVar("action", $action);
        $this->view->setVar("showPaymentDate", $showPaymentDate);
        $this->view->setVar("arrPaymentDetail", $arrPaymentDetail);
        $this->view->setVar("page", $page);
    }

    /**
     * Pre-action view to be open in pop-up for payment tracker action confirmation (opens after click on tracker action button)
     * 
     * @method paymentTrackerConfirmationAction
     * @access public
     */
    public function getOnlinePaymentModeAction($token = '') {
        try {
            if (!empty($token)) {
                $this->resource = $this->di->getShared('resource');
                $this->resource->setTokenKey('token');
                $isValidToken = $this->resource->isValid(null, $token);
                if ($isValidToken) {
                    $soc_id = $this->resource->getSocId();
                    if (!empty($soc_id)) {
                        $this->incometrackerevent->addListener('generalSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
                        $generalsettingparameter = $this->incometrackerevent->incometracker('generalSetting:getgeneralsetting', array('auth' => array('soc_id' => $soc_id)));

                        $data['admin'] = isset($generalsettingparameter['INCOME_PAYMENT_MODE']) ? explode(",", $generalsettingparameter['INCOME_PAYMENT_MODE']) : $this->constants['billpaymentmode'];
                        $data['admin'] = array_combine($data['admin'], array_map('ucfirst', $data['admin']));

                        if (isset($data['admin']['online']) && !empty($data['admin']['online'])) {
                            $data['member'] = explode(",", $generalsettingparameter['INCOME_PAYMENT_MODE_ONLINE']);
                            $data['member'] = array_combine($data['member'], array_map('ucfirst', $data['member']));
                            unset($data['admin']['online']);
                        }
                        $message = "OK";
                    }
                    //print_r($data['member']);exit;
                }
            } else {
                return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember/");
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * Returns json encoded string of Members/ Tenants mentioning the unit they reside in.
     * 
     * @method getMembersAction
     * @access public
     */
    public function getNonMembersAction() {
        $this->view->disable();
        $request = $this->request;
        $posted_values = $this->request->getPost('q');
        if ($request->isPost()) {
            $arrListenerdata = ['auth' => $this->auth, 'posted_values' => ['searchdata' => $posted_values[0]]];

            $list = array();
            if (!empty($arrListenerdata['posted_values']['searchdata'])) {
                $auth = $this->session->get('auth');
                $arrDataListener['soc_id'] = $auth['soc_id'];
                $result = $this->incometrackerevent->incometracker('nomemberincomelistner:searchMasterNonmembers', $arrListenerdata); //get all Unit details
                ////print_r($result);//exit;
                //echo '<pre>';print_r($result);exit;
                if (isset($result['data']) && !empty($result['data'])) {
                    foreach ($result['data'] as $rdr => $data) {

                        $list[$data['nonmember_id']] = ['id' => $data['nonmember_id'], 'nonmember_ledger_id' => $data['nonmember_ledger_id'], 'is_default_ledger' => $data['is_default_ledger'], 'member' => $data['nonmember_name'], 'value' => $data['nonmember_name'], 'name' => ucwords($data['nonmember_name']), 'member_email_id' => $data['email_id'], 'member_mobile_number' => $data['mobile_number']];
                    } //end of foreach
                }
            }
            //print_r($list);exit;
            echo json_encode($list); //exit;
            exit();
        }
    }

    /**
     * Cancel bill on Non member (opens after click on raise issue button)
     * 
     * @method cancelNonmemberbillAction
     * @access public
     */
    public function cancelNonmemberbillAction() {
        $id = $this->request->getPost("id", "striptags");

        if (empty($id)) {
            $arrResponse = array('status' => 'error', 'message' => 'No Record found');
            $this->session->set("err_msg", $arrResponse['message']);
            echo json_encode($arrResponse);
            exit();
        } elseif (!empty($id)) {
            $this->auth = $this->session->get("auth");
            $refundOption = $this->request->getPost("refund_option");
            $this->soc_db_w = $this->di->getShared('soc_db_w');
            $this->soc_db_w->begin();

            $arrResponse = $this->incometrackerevent->incometracker('nomemberincomelistner:cancelNonmemberInvoice', array('auth' => $this->auth, 'id' => $id, 'refund_option' => $this->request->getPost("refund_option"), 'bank_account' => $this->request->getPost("bank_account")));
            ////print_r($arrResponse);exit;
            if (!empty($arrResponse['status']) && $arrResponse['status'] == 'success') {
                $this->soc_db_w->commit();
                $this->session->set("suc_msg", $arrResponse['message']);
            } else {
                $this->soc_db_w->rollback();
                $this->session->set("err_msg", $arrResponse['message']);
            }
        }

        echo json_encode($arrResponse);
        exit();
    }

    /**
     * Returns json encoded string of Members/ Tenants mentioning the unit they reside in.
     * 
     * @method generateCommonMemberInvoiceAction
     * @access public
     */
    public function generateCommonMemberInvoiceAction() {
        //$this->view->disable();
        $request = $this->request;
        $posted_values = $this->request->getPost('q');

        if ($request->isPost()) {
            $arrListenerdata = ['auth' => $this->auth, 'posted_values' => ['searchdata' => $posted_values[0], 'member_or_tenant' => $posted_values[1], 'date' => $posted_values[2], 'type' => $posted_values[3]]];

            $list = array();
            if (!empty($arrListenerdata['posted_values']['type'])) {
                $result = $this->incometrackerevent->incometracker('MemberIncomeDetail:searchMembers', $arrListenerdata); //get all Unit details
                $auth = $this->session->get('auth');
                $arrDataListener['soc_id'] = $auth['soc_id'];
                //echo '<pre>';//print_r($result);exit;
                if (isset($result['data']) && !empty($result['data'])) {
                    foreach ($result['data'] as $rdr => $data) {
                        $arrDataListener['unit_id'] = $data['unit_id'];
                        $arrDataListener['soc_id'] = $arrListenerdata['auth']['soc_id'];

                        $msg = 'No Pending Due.';
                        $advanceAllowed = 1;

                        if (strtolower($arrListenerdata['posted_values']['type']) == 'maintenance') {
                            $unpaidInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceUnpaidBill', $arrDataListener);
                            if (empty($unpaidInvoiceDetail) && count($unpaidInvoiceDetail) < 1) {
                                $arrAllUnitInvoices = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoices', $arrDataListener);
                                if (empty($arrAllUnitInvoices) && count($arrAllUnitInvoices) < 1) {
                                    $msg = 'Invoice not generated.';
                                    $advanceAllowed = 0;
                                } else {
                                    foreach ($arrAllUnitInvoices as $eachInvoiceDetail) {
                                        if (strtolower($eachInvoiceDetail['status']) == 'in_process') {
                                            $msg = 'Cheque Submitted, Payment In Process.';
                                            $advanceAllowed = 1;
                                            break;
                                        }
                                    }
                                }
                            }
                        } elseif (strtolower($arrListenerdata['posted_values']['type']) == 'incident') {
                            $unpaidInvoiceDetail = $this->incometrackerevent->incometracker('commonBilling:getIncidentInvoiceUnpaidBill', $arrDataListener);
                        }

                        $list[$data['id']] = ['id' => $data['id'], 'member' => $data['member'], 'value' => $data['member'], 'name' => ucwords($data['member_name']), 'building' => ucwords($data['soc_building_name']), 'flat' => ucwords($data['unit_flat_number']), 'unit_id' => $data['unit_id'], 'total_due' => (!empty($unpaidInvoiceDetail['total_unpaid_invoice_amount'])) ? $unpaidInvoiceDetail['total_unpaid_invoice_amount'] : '', 'unpaidInvoices' => (!empty($unpaidInvoiceDetail)) ? $unpaidInvoiceDetail : array(), 'message' => $msg, 'advance_allowed' => $advanceAllowed];
                    } //end of foreach
                }
            }

            echo json_encode($list);
            exit();
        }

        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);
        $this->view->setVar("config", $this->config);
        //$this->view->setVar("showBankAccount", $showBankAccount);
        //$this->view->setVar("actionUrl", $actionUrl);
        //$this->view->setVar("incomeType", $incomeType);
        $this->view->setVar("form", new \ChsOne\IncomeTracker\Forms\GenerateManualBillForm(null, $arroptions));
    }

    /**
     * non member income account.
     * 
     * @method nonmemberMasterAction
     * @access public
     */
    public function nonmemberMasterAction() {
        $options = array();
        $auth = $this->session->get("auth");

        $objNonmemberIncome = new \ChsOne\Models\Nonmember();
        $arrData = $this->request->getPost();
        $number_page = $this->request->getQuery("page", "int");
        $number_page_app = ($number_page > 0) ? $number_page : 1;
        $objQueryBuiler = $objNonmemberIncome->getNonmemberMasterDetails(array('soc_id' => $auth['soc_id']));
        $paginator = new Paginatorbyquery(array("builder" => $objQueryBuiler, "limit" => PAGE_NUMBER, "page" => $number_page_app));
        $page = $paginator->getPaginate();
        $arrNonmemberMaster = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberMasterList', array('nonmember_detail' => $page->items->toArray())); //get all payment details
        //echo '<pre>';//print_r($arrNonmemberMaster);exit;
        $this->view->setVar("arrNonmemberMasterList", $arrNonmemberMaster);
        $this->view->setVar("page", $page);
        $this->view->setLayout("admin");
        $this->view->setVar("config", $this->config);
    }

    /**
     * non member income account.
     * 
     * @method nonmemberMasterAction
     * @access public
     */
    public function addnonmemberMasterAction($id = '') {
        $options = array();
        $err_msgs = [];
        $defaultLedgDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $this->auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
        if (!empty($id)) {
            $this->view->setVar('nonmember_id', $id);
            $arrListenerData['auth'] = $this->auth;
            $arrListenerData['nonmember_id'] = $id;
            $arrNonmemberResponse = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberById', $arrListenerData);
            $options['form_mode'] = 'edit';
            $options['nonmember_data'] = $arrNonmemberResponse->toArray()[0];
            if ($arrNonmemberResponse->toArray()[0]['nonmember_ledger_id'] == $defaultLedgDetails['recieving_ledger_id']) {
                $options['default_ledger_data'] = $defaultLedgDetails;
            }
        }

        $form = new AddNonMemberMasterForm($this, $options);

        if ($this->request->isPost()) {

            $data = $this->request->getPost();
            if ($form->isValid($data) == true) {
                $this->soc_db_w->begin();
                if ($data['nonmem_ledger']) {
                    $ledgerName = $data['first_name'] . ' ' . ($data['last_name'] ? $data['last_name'] : '');
                    $arrNonmemLedgerRes = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $this->auth, 'ledger_name' => $ledgerName, 'context' => SUNDRY_DEBTORS_GROUP));
                    $arrNonmemLedgerRes['is_default_ledger'] = 0;
                } else {
                    $arrNonmemLedgerRes = $defaultLedgDetails;
                    $arrNonmemLedgerRes['is_default_ledger'] = 1;
                }

                if (isset($id) && !empty($id)) {
                    $data['nonmember_id'] = $id;

                    $arrNonMemberUpdateResponse = $this->incometrackerevent->incometracker('nomemberincomelistner:updateNonmember', array('arrUpdateData' => $data, 'auth' => $this->auth, 'ledger_details' => $arrNonmemLedgerRes));
                    if ($arrNonMemberUpdateResponse['update_status'] == 'success') {
                        $this->soc_db_w->commit();

                        $this->session->set('succ_nonmember_add_update', 'Nonmember Updated Successfully.');
                        return $this->response->redirect($this->config->system->full_base_url . "income-details/nonmemberMaster");
                    } else {
                        $this->soc_db_w->rollback();
                        $this->session->set('err_nonmember_failure', 'Unable to Update Nonmember');
                    }
                } else {

                    $arrNonMemberCreateResponse = $this->incometrackerevent->incometracker('nomemberincomelistner:saveNonmember', array('arrPostData' => $data, 'auth' => $this->auth, 'ledger_details' => $arrNonmemLedgerRes));

                    if (isset($arrNonMemberCreateResponse['nonmember_id']) && !empty($arrNonMemberCreateResponse['nonmember_id'])) {
                        $this->soc_db_w->commit();
                        $this->session->set('succ_nonmember_add_update', 'Nonmember Added Successfully.');

                        return $this->response->redirect($this->config->system->full_base_url . "income-details/nonmemberMaster");
                    } else {
                        $this->soc_db_w->rollback();
                        $this->session->set('err_nonmember_failure', 'Unable to Save Nonmember');
                    }
                }
            } else {
                //collect form error messages
                foreach ($form->getMessages() as $message) {
                    $err_msgs[$message->getField()] = (string) $message;
                } // end of foreach form getMessages
            }
        }

        $this->view->setVar('err_msg', $err_msgs);
        $this->view->setVar("options", $options);
        $this->view->setVar("form", $form);
        $this->view->setLayout("admin");
        $this->view->setVar("config", $this->config);
    }

    public function createNonMemberLedger($nonMemberData = []) {
        $arrNonmemLedgerDetails = $this->incometrackerevent->incometracker('accountListener:createNonmemberLedgerExit', array('auth' => $auth, 'ledger_name' => 'Nonmember Income', 'context' => SUNDRY_DEBTORS_GROUP));
    }

    public function downloadNonMemberInvoiceAction($invoice_number = '', $download = '', $soc_id = '') {
        if (empty($invoice_number)) {
            $invoice_details = explode('/', $this->dncAction($unit_id));
            $unit_id = $invoice_details[1];
            $invoice_number = $invoice_details[2];
            $download = $invoice_details[3];
            $soc_id = $invoice_details[4];
            $timestamp = (!empty($invoice_details[5]) ? $invoice_details[5] : time());

            if (time() > $timestamp) {
                $this->session->set('succ_msg', 'Link has been expired. Try again after some time');
                if (!empty($this->session->get('auth'))) {
                    return $this->response->redirect($this->config->system->full_base_url . "income-details/incomenonmember/");
                } else {
                    return $this->response->redirect($this->config->system->full_base_url . "users/linkExpired");
                }
            }
        }

        if (!empty($soc_id)) { //if soc_id is sent as get parameter
            $auth = ['soc_id' => $soc_id];
            $conce = $this->calMultiDbFlow($auth['soc_id']);
            $this->di->setShared('dbSoc', $conce);
        } else {
            $auth = $this->session->get('auth');
        }
        if (!empty($auth)) {
            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $unit_id;
            $arrDataListener['invoice_number'] = $invoice_number;
            $arrDataListener['getSingleInvoiceDetail'] = true;
            $arrListenerData['auth'] = $this->auth;
            $arrListenerData['returnType'] = 'object';
            $arrListenerData['invoice'] = $invoice_number;
            $objNonmemberIncome = new \ChsOne\Models\NonmemberIncome();
            $objQueryBuiler = $objNonmemberIncome->getNonmemberIncomeDetails(array('soc_id' => $auth['soc_id'], 'inv_no' => $invoice_number));
            $paginator = new Paginatorbyquery(array("builder" => $objQueryBuiler, "limit" => PAGE_NUMBER, "page" => $number_page_app));
            $page = $paginator->getPaginate();

            $arrCommonBill = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonmemberInvoiceList', array('nonmember_detail' => $page->items)); //get all payment details
            $arrMemberDetail = array();
            $strMemberAssociateName = '';
            if (!empty($arrCommonBill)) {
                $arrData = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id);
                $units = new Units();
                $arrMemberDetail = current($units->getUnitMemberParkingDetails($arrData));
                $arrMemberAssociateDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitAllAssociateMemberDetails', $arrData); //get all Unit details
                if (!empty($arrMemberAssociateDetail)) {
                    $strMemberAssociateName = $this->incometrackerevent->incometracker('MemberIncomeDetail:formatAssociateMemberName', $arrMemberAssociateDetail); //get all Unit details
                }
            }
            $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
            $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
            
            $arrGeneralSettingData = array('soc_id' => $auth['soc_id'], 'setting_key' => array('INCOME_PAYMENT_INSTRUCTION', 'INCOME_SHOW_INTEREST_BREAKUP', 'INVOICE_LOGO', 'PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD'));
            $arrInvoiceGeneralSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceGeneralSetting', $arrGeneralSettingData); //get all Unit details
            //echo '<pre>'; print_r($arrInvoiceGeneralSetting); exit;
            if (!empty($arrInvoiceGeneralSetting)) {
                foreach ($arrInvoiceGeneralSetting as $key => $eachGeneralSetting) {
                    if (strtolower($eachGeneralSetting['setting_key']) == 'income_payment_instruction') {
                        $arrInvoiceGeneralNote = explode(PHP_EOL, $eachGeneralSetting['setting_value']);
                    }
                    if (strtolower($eachGeneralSetting['setting_key']) == 'income_show_interest_breakup' && $eachGeneralSetting['setting_value'] == 1) {
                        $this->view->setVar("showInterestBreakup", 1);
                    }
                    if (strtolower($eachGeneralSetting['setting_key']) == 'payment_transaction_receipt_last_period' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                        $arrLastPeriodPaymentTransaction = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLastPeriodPaymentTransaction', array('soc_id' => $auth['soc_id'], 'arrInvoiceDetail' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]));
                        if (!empty($arrLastPeriodPaymentTransaction['payment_transaction_detail']) && count($arrLastPeriodPaymentTransaction['payment_transaction_detail']) > 0) {
                            $this->view->setVar("arrLastPeriodPaymentTransaction", $arrLastPeriodPaymentTransaction);
                        }
                    }
                    if (strtolower($eachGeneralSetting['setting_key']) == 'invoice_logo' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                        $arrSocietyWebsiteWelcome = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyWebsiteWelcome', array('soc_id' => $auth['soc_id']));
                        if (!empty($arrSocietyWebsiteWelcome['soc_header_logo'])) {
                            $this->view->setVar("imageLogoUrl", $this->config->s3server_details->s3_security_protocol . $this->config->s3server_details->bucket_name . '.s3.amazonaws.com/socweb/' . $auth['soc_id'] . '/sitelogo/' . $arrSocietyWebsiteWelcome['soc_header_logo']);
                        }
                    }
                    if (strtolower($eachGeneralSetting['setting_key']) == 'show_society_signature' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                        $this->view->setVar("showSocietySignature", true);
                    }
                    if (strtolower($eachGeneralSetting['setting_key']) == 'show_chsone_footer' && strtolower($eachGeneralSetting['setting_value']) == 'yes') {
                        $this->view->setVar("showChsoneFooter", true);
                    }
                }
            }
            $arrTaxDetail = '';
            if ($arrCommonBill) {
                $tax_class = TaxClass::find("tax_class_id = " . $arrCommonBill[0]['tax_class_id']);
                $arrTaxDetail = $tax_class->toArray();
            }
            $arrAppliedTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getAppliedTaxDetail', array('soc_id' => $auth['soc_id'], 'invoice_number' => $arrCommonBill[0]['bill_number'])); //get all Unit details
            $this->view->setVar("taxColumnCount", count($arrAppliedTaxDetail));
            $arrAppliedTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getParticularTaxDetail', array('arrAppliedTaxDetail' => $arrAppliedTaxDetail)); //get all Unit details
            
            $invoiceAmount =  $arrCommonBill[0]['bill_amount']+$arrAppliedTaxDetail['particularTotalTax'];
            $advanceAmount =  $arrCommonBill[0]['advance_amount']+$arrCommonBill[0]['credit_amount'];
            $arrCommonBill[0]['total_due'] = (($invoiceAmount>$advanceAmount)) ? round($invoiceAmount-$advanceAmount,2) : 0;
            
            $rupeesInWord = '';
            $rupeesInWord = $this->incometrackerevent->incometracker('MemberIncomeDetail:numberToWordRupees', array('number' => abs($arrCommonBill[0]['total_due'])));
            $rupeesInWord = str_replace("  ", " ", $rupeesInWord);

            $arrNonmemberMasterDetail = $this->incometrackerevent->incometracker('nomemberincomelistner:getNonMemberInvoiceDetail', array('soc_id' => $auth['soc_id'], 'arrNonmemberDetail' => $arrCommonBill[0])); //get all payment details
            //echo '<pre>';print_r($arrAppliedTaxDetail);print_r($arrTaxDetail);exit;
            //echo '<pre>';print_r($arrAppliedTaxDetail['arrTaxParticular']);print_r($arrAppliedTaxDetail['arrTaxCategoryParticular']);exit;
            //$this->view->setVar("taxColumnCount", count($arrAppliedTaxDetail));
            $this->view->setVar("arrTaxDetail", $arrTaxDetail);
            $this->view->setVar("arrLateChargeTaxClass", $arrAppliedTaxDetail['arrLateChargeTaxClass']);
            $this->view->setVar("arrAppliedTaxDetail", $arrAppliedTaxDetail['arrTaxClass']);
            $this->view->setVar("arrAppliedTaxCategory", $arrAppliedTaxDetail['arrTaxParticular']);
            $this->view->setVar("arrTaxCategoryParticular", $arrAppliedTaxDetail['arrTaxCategoryParticular']);
            $this->view->setVar("arrTaxCategoryParticularSubtotal", $arrAppliedTaxDetail['arrTaxCategoryParticularSubtotal']);
            $this->view->setVar("lateChargeTotalTax", $arrAppliedTaxDetail['lateChargeTotalTax']);
            $this->view->setVar("arrAllAppliedTaxDetail", $arrAppliedTaxDetail);
            $this->view->setVar("arrNonMemberMasterDetail", $arrNonmemberMasterDetail);

            $this->view->setVar("finalInvoiceAmount", $finalInvoiceAmount);
            $this->view->setVar("outstandingPrincipalAmount", $outstandingPrincipalAmount);
            $this->view->setVar("interestAmount", $interestAmount);
            $this->view->setVar("advanceAmount", $advanceAmount);
            //$this->view->setVar("partialPaidAmount", $partialPaidAmount);
            $this->view->setVar("associateMembers", $strMemberAssociateName);
            $this->view->setVar("grandTotal", $grandTotal);
            $this->view->setVar("rupeesInWord", ucwords($rupeesInWord));
            $this->view->setVar("arrIncomeInvoiceDetail", $arrCommonBill);
            $this->view->setVar("arrMemberDetail", $arrMemberDetail);
            $this->view->setVar("arrSocietyDetail", $arrSocietyDetail);
            $this->view->setVar("arrInvoiceGeneralNote", $arrInvoiceGeneralNote);
        }

        if (!empty($download) && $download == 'downloadview') {
            $this->view->pick('incomedetails/downloadNonMemberInvoice');
        } else if (!empty($download) && $download == 'download') {
            $arrListenerdata['format'] = 'pdf';
            $arrListenerdata['unit_id'] = $unit_id;
            $arrListenerdata['invoice_number'] = $invoice_number;
            $arrListenerdata['soc_id'] = $auth['soc_id'];
            $this->invoiceexport = new \ChsOne\Components\Export\ExportEvent($this->config);
            $this->invoiceexport->addlistener('InvoiceExport', '\ChsOne\Components\Export\Listeners\InvoiceExportListener');
            $result = $this->invoiceexport->exportDocument('InvoiceExport:pdfNonMemberDocs', $arrListenerdata);
        }
        $this->view->setVar("config", $this->config);
        $this->view->setVar("type", 'print');
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setLayout("printInvoice");
    }

    /**
     * Cancel bill on Non member (opens after click on raise issue button)
     * 
     * @method cancelNonmemberbillAction
     * @access public
     */
    public function cancelMaintenanceInvoiceAction() {
        $id = $this->request->getPost("unit_invoice_id", "striptags");
        if (empty($id)) {
            $arrResponse = array('status' => 'error', 'message' => 'No Record found');
            $this->session->set("err_rule", $arrResponse['message']);
            echo json_encode($arrResponse);
            exit();
        } elseif (!empty($id)) {
            $this->auth = $this->session->get("auth");

            $this->soc_db_w = $this->di->getShared('soc_db_w');
            $this->soc_db_w->begin();

            $arrResponse = $this->incometrackerevent->incometracker('MemberIncomeDetail:cancelMaintenanceInvoice', array('auth' => $this->auth, 'unit_invoice_id' => $id, 'socAdminUsers' => $this->session->get("socUsers")['socAdminUsers']));
            if (!empty($arrResponse['status']) && strtolower($arrResponse['status']) == 'success') {
                $this->soc_db_w->commit();
                $this->session->set("succ_msg", $arrResponse['message']);
            } else {
                $this->soc_db_w->rollback();
                $this->session->set("err_rule", $arrResponse['message']);
            }
        }

        echo json_encode($arrResponse);
        exit();
    }

    /**
     * Confirm popup to ask confirmation on cancel invoice (opens after click on raise issue button)
     * 
     * @method cancelNonmemberbillAction
     * @access public
     */
    public function confirmInvoiceCancellationAction($id, $action = 'cancel') {
        $this->view->setRenderLevel(View::LEVEL_ACTION_VIEW);

        $this->view->setVar("config", $this->config);
        $this->view->setVar("unit_invoice_id", $id);
        $this->view->setVar("action", $action);
    }

    public function insertLedgerEntryAction() {
        echo '<pre>';
        $unit_id = $this->request->getQuery("unit_id");
        $particular = $this->request->getQuery("particular");
        $invoice = $this->request->getQuery("invoice");
        $amount = $this->request->getQuery("amount");
        $cr_ledger = $this->request->getQuery("ledger"); //387; 
        $taxAmount = $this->request->getQuery("taxAmount"); //387;
        $auth = $this->session->get("auth");

        //dates for ledger transaction
        $bill_date = '2018-04-01';
        $start_date = '2018-04-01';
        $end_date = '2018-06-30';
        $arrTaxAmount = array(54, 162, 108, 81, 135, 216, 189, 243, 27, 270, 324);
        $arrParticularAmount = array(150, 300, 600, 900, 1200, 1500);
        echo 'HERE WE GO ...###############################################################<br>';
        //print_r($auth);
        if (!is_numeric($amount) || empty($amount)) {
            echo '######ERROR#####<br>Abey Gadhe!!! amount to ache se dal.';
            exit();
        }
        if (!is_numeric($taxAmount) || empty($taxAmount)) {
            echo '######ERROR#####<br>Abey Gadhe!!! tax amount to ache se dal.';
            exit();
        }
        if (!is_numeric($unit_id) || empty(trim($unit_id))) {
            echo '######ERROR#####<br>Unit id ache se dekh ke dalne me sharam aa rhi h??';
            exit();
        }
        if (empty($particular) || is_numeric($particular)) {
            echo '######ERROR#####<br>You have missed the particular OR number ki jagah string dala hoga dekh jara dhyan se.';
            exit();
        }
        if (empty($invoice)) {
            echo '######ERROR#####<br>Invoice to dal fir se night marna h kya?';
            exit();
        }

        if (!in_array($taxAmount, $arrTaxAmount)) {
            echo '######ERROR#####<br>Bhai mere tera dhyan kaha h? <br>Tax amount jara check kr ke dal, bar bar batane wala nhi hu main';
            exit();
        }

        if (!in_array($amount, $arrParticularAmount)) {
            echo '######ERROR#####<br>Bhai mere tera dhyan kaha h? <br>Parking amount jara check kr ke dal, bar bar batane wala nhi hu main';
            exit();
        }

        if (!in_array($particular, array('4wheeler', '2wheeler'))) {
            echo '######ERROR#####<br>Bhai mere tera dhyan kaha h? <br>Particular name jara check kr ke dal, bar bar batane wala nhi hu main';
            exit();
        }

        if (!empty($unit_id) && !empty($auth)) {
            $soc_id = $auth['soc_id'];
            if (empty(trim($cr_ledger))) {
                if ($soc_id == 26) {
                    $cr_ledger = 387; //parking ledger id
                    $particular = 'parking-' . $particular;
                } else {
                    $objIncomeAccounts = \ChsOne\Models\IncomeAccounts::findfirst("account_type = 'member' AND account_name = " . $data['income_account_id'] . " AND soc_id = " . $soc_id);
                    if (!empty($objIncomeAccounts)) {
                        $cr_ledger = $objIncomeAccounts->fk_income_ledger_id;
                    }
                }
            }
            if (empty($cr_ledger)) {
                echo '######ERROR#####<br>Sorry!! cannot do a ledger transaction';
                exit();
            }

            $arrUnitDetails = $this->incometrackerevent->incometracker('InvoiceGenerator:getChargeableUnitDetails', array('auth' => $auth, "unit_id" => $unit_id));
            echo 'UNIT DETAIL ...###############################################################<br>';
            //print_r($arrUnitDetails);//exit;
            if (!empty($arrUnitDetails)) {
                $txnPattern = ucwords($particular) . ' against invoice ' . ucwords($invoice) . ' dated ' . $this->getDisplayDate($bill_date) . ' for period ' . $this->getDisplayDate($start_date) . ' to ' . $this->getDisplayDate($end_date);
                $txnTransaction = \ChsOne\Models\LedgerTxn::findfirst(array("soc_id = " . $soc_id . " AND memo_desc like '%" . $txnPattern . "%' AND ledger_account_id = " . $arrUnitDetails['ledger_account_id'] . " AND transaction_type = 'dr' AND transaction_amount=" . $amount));
                if (!empty($txnTransaction)) {
                    echo '######ERROR#####<br>Already entry mar chuka h, Duplicate kyu kr rha h?<br>Night maarne ka man h to akele mar na sabke sath kyu khel rha h?';
                    exit();
                }
                //Get unit ledger entry
                //$arrBookerLedgerDetails  = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth'=>$auth, 'ledger_name'=>'BLDG#'.strtoupper($arrUnitDetails['soc_building_name']).'-'.$arrUnitDetails['unit_flat_number'], 'context'=>ACCOUNT_RECEIVABLE_GROUP));
                $arrBookerLedgerDetails = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $auth, 'ledger_id' => $arrUnitDetails['ledger_account_id']));

                //Get particular ledger
                $arrIncomeAccounts = $this->incometrackerevent->incometracker('accountListener:checkledgerExistNew', array('auth' => $auth, 'ledger_id' => $cr_ledger));
                echo 'LEDGER DETAIL ...###############################################################<br>';
                //print_r($arrBookerLedgerDetails);//print_r($arrIncomeAccounts);//exit;
                if (!empty($arrBookerLedgerDetails) && !empty($arrIncomeAccounts)) {
                    $this->soc_db_w = $this->di->getShared('soc_db_w');
                    $this->soc_db_w->begin();

                    $arrListnerData['auth'] = $auth;
                    $arrListnerData['voucher_type'] = '';
                    $arrListnerData['from_ledger_id'] = $arrBookerLedgerDetails['recieving_ledger_id'];
                    $arrListnerData['to_ledger_id'] = $arrIncomeAccounts['recieving_ledger_id'];
                    $arrListnerData['transaction_date'] = $bill_date;
                    $arrListnerData['transaction_amount'] = $amount;
                    $arrListnerData['narration'] = ucwords($particular) . ' against invoice ' . ucwords($invoice) . ' dated ' . $this->getDisplayDate($bill_date) . ' for period ' . $this->getDisplayDate($start_date) . ' to ' . $this->getDisplayDate($end_date); //$arrListenerData['invoice_number'].' '.$key.' invoice generation';
                    $arrListnerData['from_ledger_name'] = $arrBookerLedgerDetails['receiver_name'];
                    $arrListnerData['to_ledger_name'] = $arrIncomeAccounts['receiver_name'];
                    $arrListnerData['payment_reference'] = '';
                    $arrListnerData['transaction_type'] = '';
                    $arrListnerData['mode_of_payment'] = '';
                    $arrListnerData['other_payment_ref'] = '';
                    echo 'LEDGER TRANSACTION DETAIL ...###############################################################<br>';
                    //print_r($arrListnerData);//exit;
                    $arrLedgerEntry = $this->incometrackerevent->incometracker('accountListener:transactionLedgerEntry', $arrListnerData);
                    if (isset($arrLedgerEntry['success'])) {
                        $likePattern = 'Tax amount on Parking against invoice ' . $invoice;
                        $txnDr = \ChsOne\Models\LedgerTxn::findfirst(array("soc_id = " . $soc_id . " AND memo_desc like '" . $likePattern . "%' AND ledger_account_id = " . $arrUnitDetails['ledger_account_id'] . " AND transaction_type = 'dr'"));
                        ////print_r($txnDr->toArray());//exit;
                        if (!empty($txnDr)) {
                            $txnDr->transaction_amount = $taxAmount;
                            ////print_r($txnDr->toArray());//exit;
                            if ($txnDr->save()) {
                                $txnCr = \ChsOne\Models\LedgerTxn::findfirst(array("soc_id = " . $soc_id . " AND memo_desc like '" . $likePattern . "%' AND ledger_account_id = 91 AND transaction_type = 'cr'"));
                                ////print_r($txnCr->toArray());//exit;
                                if (!empty($txnCr)) {
                                    $txnCr->transaction_amount = $taxAmount;
                                    /////print_r($txnCr->toArray());//exit;
                                    if ($txnCr->save()) {
                                        $this->soc_db_w->commit();
                                        echo '######SUCCESS#####<br>Congratulation TEAM CHSONE!!!';
                                        exit();
                                    } else {
                                        $this->soc_db_w->rollback();
                                        echo '######ERROR#####<br>You have failed again. Bloody looser!!! Taxes ledger update failed dhang se kr gadhe';
                                        exit();
                                    }
                                } else {
                                    $this->soc_db_w->rollback();
                                    echo '######ERROR#####<br>You have failed again. Bloody looser!!! Taxes ledger update failed dhang se kr gadhe';
                                    exit();
                                }
                            } else {
                                $this->soc_db_w->rollback();
                                echo '######ERROR#####<br>You have failed again. Bloody looser!!! ' . $arrUnitDetails['soc_building_name'] . '/' . $arrUnitDetails['unit_flat_number'] . ' ledger update failed dhang se kr gadhe';
                                exit();
                            }
                        } else {
                            $this->soc_db_w->rollback();
                            echo '######ERROR#####<br>You have failed again. Bloody looser!!! ' . $arrUnitDetails['soc_building_name'] . '/' . $arrUnitDetails['unit_flat_number'] . ' ledger update failed dhang se kr gadhe';
                            exit();
                        }
                    } else {
                        $this->soc_db_w->rollback();
                        echo '######ERROR#####<br>Parking insert failed. Bloody looser!!!';
                        exit();
                    }
                }
            }
        }
        echo '######ERROR#####<br>LOOSER LOOSER LOOSER!!!!';
        exit();
    }

    /**
     *
     * @param type $unit_id        	
     * @param type $invoice_number        	
     * @param type $download        	
     * @param type $soc_id        	
     * @return type
     */
    public function previewInvoiceAction($unit_id = '', $strPost = '', $filename = '', $soc_id = '', $generatePreview = 0) {
        if (!empty($soc_id)) {
            $auth = ['soc_id' => $soc_id];
            $conce = $this->calMultiDbFlow($auth['soc_id']);
            $this->di->setShared('dbSoc', $conce);

            $postData = unserialize(base64_decode($strPost));
            $arrUnitId = unserialize(base64_decode($unit_id));

            $start_date = $this->getDatabaseDate($postData['start_date']);
            $end_date = $this->getDatabaseDate($postData['end_date']);
            $bill_date = $this->getDatabaseDate($postData['bill_date']);

            $flagtrial = 'live';
            if (empty($this->logger)) {
                $strLogFolderPath = APP_PATH . "var/logs/" . $soc_id . "/invoicegenerate";
                if (!is_dir($strLogFolderPath)) {
                    mkdir($strLogFolderPath, 0777, true);
                }
                $strLogFilePath = $strLogFolderPath . '/' . $this->strLogFilePath;
                $this->logger = new FileAdapter($strLogFilePath);
            }
            $arrAllUnitInvoiceDetail = array();
            $arrInvoiceSetting = array();
            $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
            $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
            $arrInvoiceSetting['totalInvoiceCount'] = count($arrUnitId);
            foreach ($arrUnitId as $unit_id) {
                //print_r($unit_id);exit;
                $arrGenerateInvoiceData = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'start_date' => $this->getDatabaseDate($postData['start_date']), 'end_date' => $this->getDatabaseDate($postData['end_date']), 'action' => 'preview', 'bill_date' => $this->getDatabaseDate($postData['bill_date']), 'flagtrial' => $flagtrial, 'logger' => $this->logger, 'auth' => $auth);
                //echo '<pre>';print_r($arrGenerateInvoiceData);//exit;
                $arrInvoiceGeneratedResponse = $this->incometrackerevent->incometracker('AutoInvoice:generateInvoiceForSingleUnit', $arrGenerateInvoiceData);
                //print_r($arrInvoiceGeneratedResponse);exit;
                if (!empty($arrInvoiceGeneratedResponse['error']['value'])) {
                    $arrInvoiceSetting['showInvoiceDetail'] = 1;
                    $arrAllUnitInvoiceDetail[$unit_id]['error'] = true;
                    $arrAllUnitInvoiceDetail[$unit_id]['message'] = 'Unable to Generate Invoice for Unit '.$arrInvoiceGeneratedResponse['error']['unit'].' :<br/>' . $arrInvoiceGeneratedResponse['error']['message'];
                    continue;
//                    echo 'Unable to Generate Invoice:<br/>' . $arrInvoiceGeneratedResponse['error']['message'];
//                    exit();
                } elseif (!empty($arrInvoiceGeneratedResponse['success']['value']) && $arrInvoiceGeneratedResponse['success']['value'] == 1 && !empty($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail'])) {
                    $arrInvoiceSetting['showInvoiceDetail'] = 1;
                    $arrAllUnitInvoiceDetail[$unit_id]['error'] = false;
                    if (empty($arrInvoiceGeneralSetting)) {
                        $this->incometrackerevent->addListener('incomeGeneralSetting', 'ChsOne\Components\IncomeTracker\Setting\Listeners\GeneralSettingListener');
                        $arrInvoiceGeneralSetting = $this->incometrackerevent->incometracker('incomeGeneralSetting:getgeneralsetting', array('soc_id' => $auth['soc_id']));
                        if (!empty($arrInvoiceGeneralSetting)) {
                            if (!empty($arrInvoiceGeneralSetting['INCOME_PAYMENT_INSTRUCTION']) && isset($arrInvoiceGeneralSetting['INCOME_PAYMENT_INSTRUCTION'])) {
                                $arrInvoiceGeneralNote = explode(PHP_EOL, $arrInvoiceGeneralSetting['INCOME_PAYMENT_INSTRUCTION']);
                                $arrInvoiceSetting['arrInvoiceGeneralNote'] = $arrInvoiceGeneralNote;
                            }
                            if (!empty($arrInvoiceGeneralSetting['INCOME_SHOW_INTEREST_BREAKUP']) && isset($arrInvoiceGeneralSetting['INCOME_SHOW_INTEREST_BREAKUP']) && $arrInvoiceGeneralSetting['INCOME_SHOW_INTEREST_BREAKUP'] == 1) {
                                $arrInvoiceSetting['showInterestBreakup'] = 1;
                            }
                            if (!empty($arrInvoiceGeneralSetting['INVOICE_LOGO']) && isset($arrInvoiceGeneralSetting['INVOICE_LOGO']) && strtolower($arrInvoiceGeneralSetting['INVOICE_LOGO']) == 'yes') {
                                $arrSocietyWebsiteWelcome = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyWebsiteWelcome', array('soc_id' => $auth['soc_id']));
                                if (!empty($arrSocietyWebsiteWelcome['soc_header_logo'])) {
                                    $arrInvoiceSetting['imageLogoUrl'] = $this->config->s3server_details->s3_security_protocol . $this->config->s3server_details->bucket_name . '.s3.amazonaws.com/socweb/' . $auth['soc_id'] . '/sitelogo/' . $arrSocietyWebsiteWelcome['soc_header_logo'];
                                }
                            }
                            if (!empty($arrInvoiceGeneralSetting['SHOW_SOCIETY_SIGNATURE']) && isset($arrInvoiceGeneralSetting['SHOW_SOCIETY_SIGNATURE']) && strtolower($arrInvoiceGeneralSetting['SHOW_SOCIETY_SIGNATURE']) == 'yes') {
                                $arrInvoiceSetting['showSocietySignature'] = true;
                            }
                            if (!empty($arrInvoiceGeneralSetting['SHOW_CHSONE_FOOTER']) && isset($arrInvoiceGeneralSetting['SHOW_CHSONE_FOOTER']) && strtolower($arrInvoiceGeneralSetting['SHOW_CHSONE_FOOTER']) == 'yes') {
                                $arrInvoiceSetting['showChsoneFooter'] = true;
                            }
                            if (!empty($arrInvoiceGeneralSetting['INVOICE_FONT_SIZE']) && isset($arrInvoiceGeneralSetting['INVOICE_FONT_SIZE'])) {
                                $arrInvoiceSetting['invoiceFontSize'] = $arrInvoiceGeneralSetting['INVOICE_FONT_SIZE'];
                            }
                            if (!empty($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) && isset($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) && strtolower($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) == 'yes') {
                                $arrBankDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getDefaultBankAccount', array('soc_id' => $auth['soc_id']));
                                $arrInvoiceSetting['showBankDetail'] = 1;
                                $arrInvoiceSetting['arrBankDetail'] = $arrBankDetail;
                            }
                            if ($auth['soc_id'] == 15 || (isset($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) && strtolower($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) == 'yes')) {
                                $arrInvoiceSetting['showRateSqft'] = 1;
                            }
                        }
                    }

                    $arrAppliedTaxDetail = array();
                    $getInvoiceTaxAmount = $i = 0;

                    if (!empty($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrTaxLogDetail'])) {
                        foreach ($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrTaxLogDetail'] as $particularKey => $arrEachTaxDetail) {
                            //Get exemption tax detail
                            if (isset($arrEachTaxDetail['tax_exemption_detail'])) {
                                foreach ($arrEachTaxDetail['tax_exemption_detail'] as $eachTaxLogDetail) {
                                    $getInvoiceTaxAmount += $eachTaxLogDetail['tax_amount'];
                                    $arrAppliedTaxDetail[$i] = $eachTaxLogDetail;
                                    $arrAppliedTaxDetail[$i]['soc_id'] = $arrEachTaxDetail['soc_id'];
                                    $arrAppliedTaxDetail[$i]['invoice_number'] = $arrEachTaxDetail['invoice_number'];
                                    $arrAppliedTaxDetail[$i]['particular'] = $arrEachTaxDetail['particular'];
                                    $i++;
                                }
                            }
                            //Get actual tax detail
                            if (isset($arrEachTaxDetail['tax_detail'])) {
                                foreach ($arrEachTaxDetail['tax_detail'] as $eachTaxLogDetail) {
                                    $getInvoiceTaxAmount += $eachTaxLogDetail['tax_amount'];
                                    $arrAppliedTaxDetail[$i] = $eachTaxLogDetail;
                                    $arrAppliedTaxDetail[$i]['soc_id'] = $arrEachTaxDetail['soc_id'];
                                    $arrAppliedTaxDetail[$i]['invoice_number'] = $arrEachTaxDetail['invoice_number'];
                                    $arrAppliedTaxDetail[$i]['particular'] = $arrEachTaxDetail['particular'];
                                    $i++;
                                }
                            }
                        }
                    }

                    if (!empty($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrLateChargeTaxLogDetail'])) {

                        foreach ($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrLateChargeTaxLogDetail'] as $particularKey => $arrEachTaxDetail) {
                            //Get exemption tax detail
                            if (isset($arrEachTaxDetail['tax_exemption_detail'])) {
                                foreach ($arrEachTaxDetail['tax_exemption_detail'] as $eachTaxLogDetail) {
                                    $getInvoiceTaxAmount += $eachTaxLogDetail['tax_amount'];
                                    $arrAppliedTaxDetail[$i] = $eachTaxLogDetail;
                                    $arrAppliedTaxDetail[$i]['soc_id'] = $arrEachTaxDetail['soc_id'];
                                    $arrAppliedTaxDetail[$i]['invoice_number'] = $arrEachTaxDetail['invoice_number'];
                                    $arrAppliedTaxDetail[$i]['particular'] = $arrEachTaxDetail['particular'];
                                    $i++;
                                }
                            }
                            //Get actual tax detail
                            if (isset($arrEachTaxDetail['tax_detail'])) {
                                foreach ($arrEachTaxDetail['tax_detail'] as $eachTaxLogDetail) {
                                    $getInvoiceTaxAmount += $eachTaxLogDetail['tax_amount'];
                                    $arrAppliedTaxDetail[$i] = $eachTaxLogDetail;
                                    $arrAppliedTaxDetail[$i]['soc_id'] = $arrEachTaxDetail['soc_id'];
                                    $arrAppliedTaxDetail[$i]['invoice_number'] = $arrEachTaxDetail['invoice_number'];
                                    $arrAppliedTaxDetail[$i]['particular'] = $arrEachTaxDetail['particular'];
                                    $i++;
                                }
                            }
                        }
                    }
                    if (!empty($arrAppliedTaxDetail) && count($arrAppliedTaxDetail) > 1) {
                        foreach ($arrAppliedTaxDetail as $eachTaxDetail) {
                            $arrAllTaxClass[] = $eachTaxDetail['tax_class'];
                        }

                        $arrTaxDetail = array();
                        if (!empty($arrAllTaxClass)) {
                            $arrAllTaxClass = array_unique($arrAllTaxClass);
                            $arrAllTaxClass = array_values($arrAllTaxClass);
                            $arrTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getTaxClassDetail', array('soc_id' => $auth['soc_id'], 'tax_class_name' => $arrAllTaxClass)); //get all Unit details
                        }
                        //}
                        $arrAppliedTaxDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getParticularTaxDetail', array('arrAppliedTaxDetail' => $arrAppliedTaxDetail));
                    }
                $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
                $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
                
                $arrMemberDetail = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrMemberDetail'];
                $arrMemberDetail['soc_building_name'] = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['arrUnitData']['soc_building_name'];
                $arrMemberDetail['soc_building_floor'] = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['arrUnitData']['soc_building_floor'];
                $arrMemberDetail['unit_flat_number'] = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['arrUnitData']['unit_flat_number'];
                $arrMemberDetail['unit_area'] = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['arrUnitData']['unit_area'];
                $arrMemberDetail['unit_open_area'] = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['arrUnitData']['unit_open_area'];
                
                $units = new Units();

                $arrUnitParkingDetail = current($units->getUnitParkingDetails(array('soc_id' => $arrMemberDetail['soc_id'], 'unit_id' => $arrMemberDetail['fk_unit_id'])));
                $arrMemberDetail = current($units->getUnitMemberDetailByUnitId(array('soc_id' => $arrMemberDetail['soc_id'], 'unit_id' => $arrMemberDetail['fk_unit_id'])));
                if(!empty($arrUnitParkingDetail))
                {
                    $arrMemberDetail['parking_number'] = $arrUnitParkingDetail['parking_number'];
                    $arrMemberDetail['parking_type'] = $arrUnitParkingDetail['parking_type'];
                    $arrMemberDetail['allotment_for'] = $arrUnitParkingDetail['allotment_for'];
                    unset($arrUnitParkingDetail);
                }
 
                if (count($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrParticularDetail']) > 0) {
                    $arrIncomeInvoice = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice'];
                    $totalInvoiceAmount = $totalTaxApplicable = $totalTaxExemption = 0;
                    $finalInvoiceAmount = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['roundoff_amount'];
                    foreach ($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrParticularDetail'] as $eachParticular) {
                        if ($eachParticular['amount'] > 0) {
                            $eachParticular['particular_name'] = $eachParticular['particular'];
                            if (strtolower($eachParticular['particular']) == 'noc') {
                                $eachParticular['particular_name'] = 'Non-OccupancyCharges';
                            } elseif (strtolower($eachParticular['particular']) == 'maintenancefee') {
                                $eachParticular['particular_name'] = 'MaintenanceCharges';
                            }
                            $eachParticular['fk_rule_id'] = $eachParticular['rule_id'];

                                $eachParticular['particular_name'] = ucwords(preg_replace('/([A-Z])/', ' $1', $eachParticular['particular_name']));

                                $arrIncomeInvoice['invoice_particulars'][] = $eachParticular;
                                $finalInvoiceAmount += $eachParticular['amount'];
                                $totalInvoiceAmount += $eachParticular['amount'];
                                if (!empty($eachParticular['tax_exemptions'])) {
                                    $arrExemption = unserialize($eachParticular['tax_exemptions']);
                                    $totalTaxExemption = $arrExemption['total'];
                                    $finalInvoiceAmount += $arrExemption['total'];
                                }
                                if (!empty($eachParticular['tax_applicable'])) {
                                    $arrTaxApplicable = unserialize($eachParticular['tax_applicable']);
                                    $totalTaxApplicable = $arrTaxApplicable['total'];
                                    $finalInvoiceAmount += $arrTaxApplicable['total'];
                                }
                            }
                        }
                    }

                    $outstandingPrincipalAmount = $outstandingInterestAmount = 0; //$arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['principal_amount'];
                    $interestAmount = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['interest_amount'];
                    $advanceAmount = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['advance_amount'];
                    if (!empty($arrIncomeInvoice)) {
                        $arrIncomeInvoice['unit_invoice_number'] = $arrIncomeInvoice['invoice_number'];
                        $arrIncomeInvoice['invoice_date'] = $this->getDisplayDate($arrIncomeInvoice['bill_date']);
                        $arrIncomeInvoice['from_date'] = $arrIncomeInvoice['start_date'];
                        $arrIncomeInvoice['to_date'] = $arrIncomeInvoice['end_date'];
                        $arrIncomeInvoiceDetail['unit_invoice_detail'][0] = $arrIncomeInvoice;
                        $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail'] = array('totalInvoiceAmount' => $totalInvoiceAmount, 'totalTaxApplicable' => $totalTaxApplicable, 'totalTaxExemption' => $totalTaxExemption, 'finalInvoiceAmount' => $finalInvoiceAmount);
                    }

                    if (strtolower($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['payment_status']) == 'paid') {
                        $grandTotal = 0;
                        if (!empty($interestAmount)) {
                            $finalInvoiceAmount += $interestAmount;
                            if (!empty($arrAppliedTaxDetail['lateChargeTotalTax']) && $arrAppliedTaxDetail['lateChargeTotalTax'] > 0) {
                                $finalInvoiceAmount += $arrAppliedTaxDetail['lateChargeTotalTax'];
                            }
                        }

                    //}
                    if (!empty($advanceAmount) && $advanceAmount > 0) {
                           $grandTotal = (float) round($finalInvoiceAmount - $advanceAmount, 3);
                    }
                    //Add Principal arrear
                    if(!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal']) && $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal']>0)
                    {
                        $outstandingPrincipalAmount += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'];
                        $grandTotal += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'];
                    }
                    //Add Interest arrear
                    if(!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest']) && $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest']>0)
                    {
                        $outstandingInterestAmount = $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'];
                        $grandTotal += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'];
                    }
                }
                else
                {
//                    $grandTotal = $finalInvoiceAmount;
//                    if (!empty($advanceAmount) && $advanceAmount > 0) {
//                        $advanceAmount = $advanceAmount;//round($advanceAmount, 2);
//                        if ($advanceAmount < $finalInvoiceAmount) {
//                            $grandTotal = (float) round($finalInvoiceAmount - $advanceAmount, 3);
//                        }
//                    } else {
                        $grandTotal = $finalInvoiceAmount;
                        if (!empty($advanceAmount) && $advanceAmount > 0) {
                            $advanceAmount = $advanceAmount; //round($advanceAmount, 2);
                            if ($advanceAmount < $finalInvoiceAmount) {
                                $grandTotal = (float) round($finalInvoiceAmount - $advanceAmount, 3);
                            }
                        } elseif ($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['principal_amount'] > 0) {
                            $arrPreviousInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getAllInvoiceNumber', array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id));
                            if (count($arrPreviousInvoiceDetail) == 0) {
                                $outstandingPrincipalAmount = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['principal_amount']; //round($arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrUnitInvoice']['principal_amount'], 2);
                                $grandTotal = $finalInvoiceAmount + $outstandingPrincipalAmount; //round($finalInvoiceAmount + $outstandingPrincipalAmount, 2); //exit;
                            }
                        }

                        if (!empty($interestAmount) && $interestAmount > 0) {
                            $interestAmount = $interestAmount; //round($interestAmount, 2);
                            $grandTotal += $interestAmount;
                        }
                        //Add Principal arrear
                        if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal']) && $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'] > 0) {
                            $outstandingPrincipalAmount += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'];
                            $grandTotal += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_principal'];
                        }
                        //Add Interest arrear
                        if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest']) && $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'] > 0) {
                            $outstandingInterestAmount = $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'];
                            $grandTotal += $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['outstanding_interest'];
                        }
                        if (!empty($arrAppliedTaxDetail['lateChargeTotalTax']) && $arrAppliedTaxDetail['lateChargeTotalTax'] > 0) {
                            $grandTotal += $arrAppliedTaxDetail['lateChargeTotalTax'];
                        }
                    //}

                }
                
                    $defaultTableCoumnCount = 1;

                    //Remove zero amount particular from invoice
                    if (!empty($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars']) && count($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars']) > 0) {
                        $arrIncomeAccounts = $this->incometrackerevent->incometracker('MemberIncomeDetail:getIncomeAccountDetails', array('soc_id' => $auth['soc_id'], 'arrAccountType' => array('member', 'nonmember'), 'order_by' => 'display_order_id', 'order' => 'asc')); //get all Unit details

                        $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceParticularDisplayOrder', array('soc_id' => $auth['soc_id'], 'arrInvoiceParticulars' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'], 'arrIncomeAccounts' => $arrIncomeAccounts)); //get all Unit details

                        $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:removeDeactivatedParticular', array('arrInvoiceParticular' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'])); //get all Unit details
                        //Get rule detail
                        if (isset($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) && strtolower($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) == 'yes') {
                            $defaultTableCoumnCount = 2;
                            //$this->view->setVar("showRateSqft", 1);
                            //$arrAllUnitInvoiceDetail[$unit_id]['showRateSqft'] = 1;
                            $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getRateCalculationChargesParticular', array('arrInvoiceParticular' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'], 'arrMemberDetail' => $arrMemberDetail, 'arrIncomeAccounts' => $arrIncomeAccounts));
                        }
                        if ($auth['soc_id'] == 15) {
                            $defaultTableCoumnCount = 2;
                            $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getFourDRuleAmountDetail', array('arrInvoiceParticular' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_particulars'], 'unit_type' => $arrMemberDetail['unit_type'])); //get all Unit details
                        }
                    }

                    $arrAllUnitInvoiceDetail[$unit_id]['showBankDetail'] = 0;
                    $arrAllUnitInvoiceDetail[$unit_id]['invoiceFontSize'] = 9;
                    if (!empty($arrInvoiceGeneralSetting)) {
                        if (!empty($arrInvoiceGeneralSetting['PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD']) && isset($arrInvoiceGeneralSetting['PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD']) && strtolower($arrInvoiceGeneralSetting['PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD']) == 'yes') {
                            $arrLastPeriodPaymentTransaction = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLastPeriodPaymentTransaction', array('soc_id' => $auth['soc_id'], 'arrInvoiceDetail' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]));
                            if (!empty($arrLastPeriodPaymentTransaction['payment_transaction_detail']) && count($arrLastPeriodPaymentTransaction['payment_transaction_detail']) > 0) {
                                $arrAllUnitInvoiceDetail[$unit_id]['arrLastPeriodPaymentTransaction'] = $arrLastPeriodPaymentTransaction;
                            }
                        }
                        if (!empty($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) && isset($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) && strtolower($arrInvoiceGeneralSetting['SHOW_BANK_DETAIL']) == 'yes') {
                            if (count($arrAppliedTaxDetail['arrTaxParticular']) == 0 && (empty($finalInvoiceAmount) || strtolower($arrInvoiceGeneralSetting['SHOW_RULE_CHARGES']) == 'no')) {
                                $defaultTableCoumnCount++;
                                $arrAllUnitInvoiceDetail[$unit_id]['showBankWithoutTaxParitcular'] = $defaultTableCoumnCount;
                            }
                        }
                    }

                    if ($auth['soc_id'] == 38) {
                        $arrAllUnitInvoiceDetail[$unit_id]['hideParkingUnit'] = 1;
                    }
                    // round up grand total amount
                    $grandTotal = (float) round($grandTotal, 2);
                    $rupeesInWord = '';
                    $rupeesInWord = $this->incometrackerevent->incometracker('MemberIncomeDetail:numberToWordRupees', array('number' => abs($grandTotal)));
                    $rupeesInWord = str_replace("  ", " ", $rupeesInWord);
                }

                if (!empty($bill_date)) {
                    $invoiceDate = (strrchr($bill_date, '/')) ? $this->getDatabaseDate($bill_date) : $bill_date;
                    $arrLateChargesDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getLatePaymentChargesByInvoiceDate', array('soc_id' => $auth['soc_id'], 'invoice_from_date' => $invoiceDate));
                }
                if (strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_first_invoice']) == strtolower($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_invoice_number'])) {
                    $arrPendingOutstanding = $this->incometrackerevent->incometracker('InvoiceGenerator:getPendingOutstanding', array('soc_id' => $auth['soc_id'], 'unit_id' => $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['unit_id'], 'bill_type' => 'maintenance'));
                    if ($arrPendingOutstanding['delayed_payment_charges'] > 0) {
                        $outstandingInterestAmount = $arrPendingOutstanding['interest_amount'];
                        $interestAmount = $arrPendingOutstanding['delayed_payment_charges'];
                    } else {
                        $outstandingInterestAmount = $interestAmount;
                        $interestAmount = $arrPendingOutstanding['delayed_payment_charges'];
                    }
                }
                //echo '<pre>heelo';print_r($arrIncomeInvoiceDetail);exit;
                $arrAllUnitInvoiceDetail[$unit_id]['invoiceTaxAmount'] = $getInvoiceTaxAmount;

                $arrAllUnitInvoiceDetail[$unit_id]['arrLateChargesDetail'] = $arrLateChargesDetail;

                $arrAllUnitInvoiceDetail[$unit_id]['taxColumnCount'] = count($arrAppliedTaxDetail['arrTaxParticular']);

                $arrAllUnitInvoiceDetail[$unit_id]['originalColumnCount'] = $defaultTableCoumnCount;

                $arrAllUnitInvoiceDetail[$unit_id]['defaultColumnCount'] = $defaultTableCoumnCount;

                $arrAllUnitInvoiceDetail[$unit_id]['arrTaxDetail'] = $arrTaxDetail;

                $arrAllUnitInvoiceDetail[$unit_id]['arrLateChargeTaxClass'] = $arrAppliedTaxDetail['arrLateChargeTaxClass'];

                $arrAllUnitInvoiceDetail[$unit_id]['arrAppliedTaxDetail'] = $arrAppliedTaxDetail['arrTaxClass'];

                $arrAllUnitInvoiceDetail[$unit_id]['arrAppliedTaxCategory'] = $arrAppliedTaxDetail['arrTaxParticular'];

                $arrAllUnitInvoiceDetail[$unit_id]['arrTaxCategoryParticular'] = $arrAppliedTaxDetail['arrTaxCategoryParticular'];

                $arrAllUnitInvoiceDetail[$unit_id]['arrTaxCategoryParticularSubtotal'] = $arrAppliedTaxDetail['arrTaxCategoryParticularSubtotal'];

                $arrAllUnitInvoiceDetail[$unit_id]['lateChargeTotalTax'] = $arrAppliedTaxDetail['lateChargeTotalTax'];

                $arrAllUnitInvoiceDetail[$unit_id]['arrAllAppliedTaxDetail'] = $arrAppliedTaxDetail;

                $arrAllUnitInvoiceDetail[$unit_id]['invoiceTotalAmount'] = number_format(round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['totalInvoiceAmount'] + $getInvoiceTaxAmount + $interestAmount, 2), 2, '.', '');

                $arrAllUnitInvoiceDetail[$unit_id]['grandTotalAmount'] = number_format(round($arrIncomeInvoiceDetail['unit_invoice_detail'][0]['invoice_amount_detail']['totalInvoiceAmount'] + $getInvoiceTaxAmount + $interestAmount + $arrIncomeInvoiceDetail['unit_invoice_detail'][0]['roundoff_amount'] - $advanceAmount, 2), 2, '.', '');

                $arrAllUnitInvoiceDetail[$unit_id]['finalInvoiceAmount'] = $finalInvoiceAmount;

                $arrAllUnitInvoiceDetail[$unit_id]['outstandingPrincipalAmount'] = $outstandingPrincipalAmount;

                $arrAllUnitInvoiceDetail[$unit_id]['outstandingInterestAmount'] = $outstandingInterestAmount;

                $arrAllUnitInvoiceDetail[$unit_id]['interestAmount'] = $interestAmount;

                $arrAllUnitInvoiceDetail[$unit_id]['advanceAmount'] = $advanceAmount;

                $arrAllUnitInvoiceDetail[$unit_id]['associateMembers'] = $strMemberAssociateName;

                $arrAllUnitInvoiceDetail[$unit_id]['balanceDue'] = $grandTotal;

                $arrAllUnitInvoiceDetail[$unit_id]['rupeesInWord'] = ucwords($rupeesInWord);

                $arrAllUnitInvoiceDetail[$unit_id]['arrIncomeInvoiceDetail'] = $arrIncomeInvoiceDetail['unit_invoice_detail'];

                $arrAllUnitInvoiceDetail[$unit_id]['arrMemberDetail'] = $arrMemberDetail;

                $arrAllUnitInvoiceDetail[$unit_id]['arrLatePaymentStack'] = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['arrLatePaymentParticulars'];

                $arrAllUnitInvoiceDetail[$unit_id]['latePaymentamount'] = 0;

                $arrAllUnitInvoiceDetail[$unit_id]['intrestPrinciple'] = $arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['intrestPrinciple'];

                $arrAllUnitInvoiceDetail[$unit_id]['arrInvoiceType'] = array('invoice_type' => array('preview'));
            }

            $this->view->setVar("arrAllUnitInvoiceDetail", $arrAllUnitInvoiceDetail);
            $this->view->setVar("arrInvoiceSetting", $arrInvoiceSetting);
            $this->view->setVar("arrSocietyDetail", $arrSocietyDetail);
            $this->view->setVar("arrInvoiceType", array('invoice_type' => array('Preview'))); //$arrInvoiceGeneratedResponse['arrInvoicePreviewDetail']['latePaymentamount']); //


            $this->view->pick('incomedetails/downloadviewInvoice');
            $this->view->setVar("invoiceType", 'preview');
            $this->view->setVar("config", $this->config);
            $this->view->setVar("type", 'print');
            $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
            $this->view->setLayout("printInvoice");
        } else {

            $auth = $this->session->get('auth');
            $postData = $this->request->getPost();
            $filename = (!empty($filename)) ? str_replace(' ', '', $filename) : 'UnitPreview';
            $arrListenerdata['format'] = 'pdf';
            $arrListenerdata['unit_id'] = $unit_id;
            $arrListenerdata['unit_name'] = str_replace(' ', '', $filename);
            if (empty($postData)) {
                $postData = json_decode($strPost, true);
                $postData['start_date'] = strrchr($postData['start_date'], '-') ? $this->getDisplayDate($postData['start_date']) : $postData['start_date'];
                $postData['end_date'] = strrchr($postData['end_date'], '-') ? $this->getDisplayDate($postData['end_date']) : $postData['end_date'];
                $postData['bill_date'] = strrchr($postData['bill_date'], '-') ? $this->getDisplayDate($postData['bill_date']) : $postData['bill_date'];
                $postData['unit_id'] = $unit_id;
            }

            $arrListenerdata['unit_id'] = base64_encode(serialize(json_decode($postData['unit_id'], true)));

            $arrListenerdata['unit_name'] = $filename;
            $arrListenerdata['soc_id'] = $auth['soc_id'];
            $arrListenerdata['arrInvoiceDate'] = base64_encode(serialize(array('start_date' => $postData['start_date'], 'end_date' => $postData['end_date'], 'bill_date' => $postData['bill_date'])));
            $result = $this->invoiceexport->exportDocument('InvoiceExport:invoicePreviewPdfDocs', $arrListenerdata);
            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember");
        }
    }

    public function sendSmstoSeaBreezeAction() {
        echo '<pre>';
        $auth = $arrData = $this->session->get("auth");
        if (!empty($auth)) {
            $unit_id = $this->request->getQuery("unit_id");
            //echo $unit_id;//exit;
            $number_page = $this->request->getQuery("page", "int");
            if (empty(trim($unit_id)) && empty(trim($number_page))) {
                echo 'Please pass page or unit_id as parameter';
                exit();
            }
            $number_page = ($number_page > 0) ? $number_page : 1;

            $arrAllMemberIncome = array();

            $units = Units::find(['conditions' => 'soc_id = "' . $auth['soc_id'] . '" AND status = 1', 'columns' => 'unit_id,soc_building_name,unit_flat_number']);
            $members = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPrimaryMember', $auth);

            $this->view->setVar('units', $units);
            $this->view->setVar('members', $members);

            if (!empty($unit_id)) {
                $arrData['searchData']['unit_id'] = array($unit_id);
                $number_page = 1;
            }
            //print_r($arrData);//exit;
            $units = new Units();
            $objQueryBuiler = $units->getUnitPrimaryMemberDetails($arrData);
            $paginator = new Paginatorbyquery(array("builder" => $objQueryBuiler, "limit" => 5, //PAGE_NUMBER
                "page" => $number_page));
            $page = $paginator->getPaginate();
            $i = 1;
            foreach ($page->items as $key => $value) {
                if (!isset($arrAllMemberIncome[$value->unit_id]) && empty($arrAllMemberIncome[$value->unit_id])) {
                    $singleMemberDetail['soc_id'] = $value->soc_id;
                    $singleMemberDetail['unit_id'] = $value->unit_id;
                    $singleMemberDetail['chargeable'] = $value->chargeable;
                    $is_generatedOutstanding = $this->_isGeneratedOutstanding($value->unit_id);
                    $singleMemberDetail['is_generatedOutstanding'] = ($is_generatedOutstanding['count'] > 0) ? 1 : 0;
                    $singleMemberDetail['is_generatedBill'] = ($is_generatedOutstanding['is_already_generated'] == 0) ? 1 : 0;
                    $singleMemberDetail['unit_flat_number'] = $value->unit_flat_number;
                    $singleMemberDetail['unit_category'] = $value->unit_category;
                    $singleMemberDetail['soc_building_floor'] = $value->soc_building_floor;
                    $singleMemberDetail['soc_building_id'] = $value->soc_building_id;
                    $singleMemberDetail['soc_building_name'] = $value->soc_building_name;
                    $singleMemberDetail['unit_type'] = $value->unit_type;
                    $singleMemberDetail['id'] = $value->id;
                    $singleMemberDetail['member_first_name'] = $value->member_first_name;
                    $singleMemberDetail['member_last_name'] = $value->member_last_name;
                    $singleMemberDetail['member_mobile_number'] = $value->member_mobile_number;
                    $singleMemberDetail['member_email_id'] = $value->member_email_id;
                    $singleMemberDetail['invoice_detail'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceDetail', $singleMemberDetail); //get all Unit details
                    $arrAllMemberIncome[$value->unit_id] = $singleMemberDetail;
                    //echo number_format(ceil($singleMemberDetail['invoice_detail']['total_unpaid_invoice_amount']),2,'.', '');exit;
                    //Send pending due SMS
                    //echo $i . ': ' . $singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['unit_invoice_number'];
                    if ($singleMemberDetail['invoice_detail']['total_unpaid_invoice_amount'] > 0) {
                        $arrEmailData['soc_id'] = $auth['soc_id'];
                        $arrEmailData['title'] = 'invoice_payment_reminder_after_due_date';
                        $arrEmailData['bill_number'] = $singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['unit_invoice_number'];
                        $arrEmailData['currency'] = 'Rs.';
                        $arrEmailData['total_amount'] = number_format($singleMemberDetail['invoice_detail']['total_unpaid_invoice_amount'], 2, '.', '');
                        $arrEmailData['mobile_number'] = $singleMemberDetail['member_mobile_number'];
                        $arrEmailData['due_date'] = $singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['due_date'];
                        $arrSocietyData = array('soc_id' => $auth['soc_id'], 'status' => 1);
                        $arrSocietyDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getSocietyDetail', $arrSocietyData); //get all Unit details
                        $arrEmailData['society_name'] = ucwords($arrSocietyDetail['soc_name']);
			$arrEmailData['chsone_tiny_url'] = CHSONE_TINY_URL;

                        $smsResponse = $emailResponse = false;
                        if (!empty(trim($arrEmailData['mobile_number'])) && strlen(trim($arrEmailData['mobile_number'])) == 10) {
                            $smsResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendSmsFromTemplate', $arrEmailData);
                        }
                        if (!empty($smsResponse) && strstr($smsResponse, 'MsgID')) {
                            echo ' || Success, Sent SMS';
                        } else {
                            echo ' || Failed Sms, Try again using unit id ' . $singleMemberDetail['unit_id'] . '||Mobile No:' . $arrEmailData['mobile_number'];
                        }

                        //Uncomment this code to send email of pending dues
                        $arrEmailData['total_amount'] = number_format($singleMemberDetail['invoice_detail']['total_unpaid_invoice_amount'], 2, '.', '');
                        $arrEmailData['member_name'] = ucwords($singleMemberDetail['member_first_name'] . ' ' . $singleMemberDetail['member_last_name']);
                        $arrEmailData['email'] = array($singleMemberDetail['member_first_name'] => $singleMemberDetail['member_email_id']);
                        $arrEmailData['priority'] = \ChsOne\Components\Email\Email::PRIORITY_SEND_IMMEDIATELY;
                        $arrEmailData['chsone_site_link'] = $this->config->system->full_base_url_fe;
                        
                        $arrEmailData['from_date'] = $this->getDisplayDate($singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['from_date']);
                        $arrEmailData['to_date'] = $this->getDisplayDate($singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['to_date']);
                        $arrEmailData['bill_date'] = $this->getDisplayDate($singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['invoice_date']);
                        $arrEmailData['bill_status'] = ucwords(str_replace('_', ' ', $singleMemberDetail['invoice_detail']['unit_invoice_detail'][0]['payment_status']));
                        if (isset($singleMemberDetail['member_email_id']) && !empty(trim($singleMemberDetail['member_email_id']))) {
                            $emailResponse = $this->incometrackerevent->incometracker('AutoInvoice:sendEmailFromTemplate', $arrEmailData);
                            echo ' || Success, Sent Email';
                        } else {
                            echo ' || Failed Email, Try again using unit id ' . $singleMemberDetail['unit_id'] . '||Email Id:' . $singleMemberDetail['member_email_id'] . '';
                        }
                        sleep(5);
                    } else {
                        echo ' || No Pending Dues, No Reminder <br>';
                    }
                    $i++;
                    echo '<br/>';
                }
            }
        }
        exit;
    }

    /**
     * Get tax values and perticular rate (Ajax)
     * 
     * @param int   $id
     * @param int   $soc_id
     * @param float $value
     * @return json
     */
    public function getTaxRateByTaxIdAction() {
        $auth = $this->session->get("auth");
        $taxId = $this->request->getPost("tax_id", "striptags");
        $particularAmount = $this->request->getPost("amount", "striptags");
        if (is_numeric($taxId)) {
            $arrTaxDetail = $this->incometrackerevent->incometracker('InvoiceGenerator:getAllAppliedTaxes', array('soc_id' => $auth['soc_id'], 'arrRuleDetail' => array('applicable_taxes' => $taxId), 'totalCharge' => $particularAmount));
            //echo $arrTaxDetail['totalcharge']; exit;
            if (!empty($arrTaxDetail['totalcharge'])) {
                echo json_encode(array('taxAmount' => $arrTaxDetail['totalcharge']));
                exit();
            }
        }
        echo 0;
        exit;
    }

    /**
     * TDS payble set view
     */
    public function tdspayableReportAction() {

        // Filter 1 value
        $data['from_date'] = ($this->request->get('from_date')) ? $this->request->get('from_date') : date('d/m/Y', strtotime('first day of last month'));

        // Filter 2 value
        $data['to_date'] = ($this->request->get('to_date')) ? $this->request->get('to_date') : date('d/m/Y', strtotime('last day of last month'));
        $data['page'] = $this->request->getQuery("page", "int");
        $data['is_paging'] = 1;
        $dataArr = $this->invoiceexport->exportDocument('ReportExport:tdspayableReport', $data);
        $dataArr['meta']['soc_name'] = $this->session->get("soc_name");
//        $this->print_r($data);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $dataArr);
        $this->view->setLayout("report");
    }

    /**
     * TDS payble set print layout
     */
    public function tdspayableReportPrintAction($pdf = 0, $soc_id = 0) {
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setVar("config", $this->config);

        // Filter 1 value
        $data['from_date'] = ($this->request->get('from_date')) ? $this->request->get('from_date') : date('d/m/Y', strtotime('first day of last month'));

        // Filter 2 value
        $data['to_date'] = ($this->request->get('to_date')) ? $this->request->get('to_date') : date('d/m/Y', strtotime('last day of last month'));
        $data['is_paging'] = 1;

        if ($soc_id) {
            $conce = $this->calMultiDbFlow($soc_id);
            $this->di->setShared('dbSoc', $conce);
        } else {
            $soc_id = $this->session->get("auth")['soc_id'];
        }
        $dataArr = $this->invoiceexport->exportDocument('ReportExport:tdspayableReport', $data);
        $dataArr['meta']['soc_name'] = ($this->session->get("soc_name")) ? $this->session->get("soc_name") : \ChsOne\Models\Society::findFirst($soc_id)->soc_name;
//        $this->print_r($dataArr['data']);
        $dataArr['data']['data']->items = array();
        for ($i = 1; $i <= $dataArr['data']['data']->last; $i++) {
            $data['page'] = $i;
            $print_data = $this->invoiceexport->exportDocument('ReportExport:tdspayableReport', $data);
            $dataArr['data']['data']->items[($i - 1)] = $print_data['data']['data']->items;
            $dataArr['data']['data']->items[($i - 1)]['last_row'] = $print_data['data']['last_row'];
        }
//        $this->print_r($dataArr);
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $dataArr);
        $this->view->setVar("pdf", $pdf);
        $this->view->setVar("soc_id", $soc_id);
        $this->view->setLayout("reportPrint");

        // For landscape orientation, set $data['data']['orientation'] = 'landscape';
    }

    /**
     * TDS payble set print layout
     */
    public function tdspayableReportExcelAction() {
        // Filter 1 value
        $data['from_date'] = ($this->request->get('from_date')) ? $this->request->get('from_date') : date('d/m/Y', strtotime('first day of last month'));

        // Filter 2 value
        $data['to_date'] = ($this->request->get('to_date')) ? $this->request->get('to_date') : date('d/m/Y', strtotime('last day of last month'));
        $data['is_paging'] = 0;
//        $arrListenerdata           = $this->getData($data);
        $arrListenerdata = $this->invoiceexport->exportDocument('ReportExport:tdspayableReport', $data);
        // Let's set replace values before taking excel export
        foreach ($arrListenerdata['data']['data']->items as $key => &$data) {
            foreach ($data as $innerKey => &$innerData) {
                if (array_key_exists($innerKey, $arrListenerdata['data']['replace_values'])) {
                    $innerData = array_key_exists($innerData, $arrListenerdata['data']['replace_values'][$innerKey]) ? $arrListenerdata['data']['replace_values'][$innerKey][$innerData] : $innerData;
                }
            }
        }
        $arrListenerdata['format'] = 'excel';
        $arrListenerdata['auth'] = $this->session->get("auth");
//        $this->print_r($arrListenerdata['data']);
        $result = $this->invoiceexport->exportDocument('ReportExport:ReportsExcel', $arrListenerdata);
        $this->view->disable();
    }

    /**
     * Non-Members Receivable  set view
     */
    public function nonMembersReceivableReportAction() {
        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');

        $data['page'] = $this->request->getQuery("page", "int");
//        echo '<pre>', print_r($this->request->get());exit;
        if ($this->request->get()['save'] == 'Apply') {
            $data['page'] = 1;
        }
        $data['is_paging'] = 1;
        $dataArr = $this->invoiceexport->exportDocument('ReportExport:nonMembersReceivableReport', $data);
        $dataArr['meta']['soc_name'] = $this->session->get("soc_name");
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $dataArr);
        $this->view->setLayout("report");
    }

    public function nonMembersReceivableReportPrintAction($pdf = 0, $soc_id = 0) {
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setVar("config", $this->config);

        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');
        $data['is_paging'] = 1;
        $data['custom_paging'] = 20;

        if ($soc_id) {
            $conce = $this->calMultiDbFlow($soc_id);
            $this->di->setShared('dbSoc', $conce);
        } else {
            $soc_id = $this->session->get('auth')['soc_id'];
        }

        $dataArr = $this->invoiceexport->exportDocument('ReportExport:nonMembersReceivableReport', $data);
        $dataArr['meta']['soc_name'] = ($this->session->get("soc_name")) ? $this->session->get("soc_name") : \ChsOne\Models\Society::findFirst($soc_id)->soc_name;
//        $this->print_r($dataArr['data']);
        $dataArr['data']['data']->items = array();
        for ($i = 1; $i <= $dataArr['data']['data']->last; $i++) {
            $data['page'] = $i;
            $print_data = $this->invoiceexport->exportDocument('ReportExport:nonMembersReceivableReport', $data);
            $dataArr['data']['data']->items[($i - 1)] = $print_data['data']['data']->items;
            $dataArr['data']['data']->items[($i - 1)]['last_row'] = $print_data['data']['last_row'];
        }
//        $this->print_r($dataArr);
//        echo '<pre>', print_r($dataArr);exit;
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $dataArr);
        $this->view->setVar("pdf", $pdf);
        $this->view->setVar("soc_id", $soc_id);
        $this->view->setLayout("reportPrint");

        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setVar("config", $this->config);

        // For landscape orientation, set $data['data']['orientation'] = 'landscape';
    }

    public function nonMembersReceivableReportExcelAction() {
        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');

        $data['is_paging'] = 0;
        $arrListenerdata = $this->invoiceexport->exportDocument('ReportExport:nonMembersReceivableReport', $data);
        // Let's set replace values before taking excel export
        foreach ($arrListenerdata['data']['data']->items as $key => &$data) {
            foreach ($data as $innerKey => &$innerData) {
                if (array_key_exists($innerKey, $arrListenerdata['data']['replace_values'])) {
                    $innerData = array_key_exists($innerData, $arrListenerdata['data']['replace_values'][$innerKey]) ? $arrListenerdata['data']['replace_values'][$innerKey][$innerData] : $innerData;
                }
            }
        }
        $arrListenerdata['format'] = 'excel';
        $arrListenerdata['auth'] = $this->session->get("auth");
//        echo '<pre>', print_r($arrListenerdata);exit;
        $result = $this->invoiceexport->exportDocument('ReportExport:ReportsExcel', $arrListenerdata);
        $this->view->disable();
    }

    public function nonMembersReceivableReportPDFAction() {
        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');

        $data['url'] = $this->config->system->full_base_url . '/income-details/nonMembersReceivableReportPrint/1/' . $this->session->get("auth")['soc_id']
                . '?search_by=' . $data['search_by'] . '&search_value=' . $data['search_value'] . '&filter_by=' . $data['filter_by'];
        $data['file_name'] = 'NonmembersReceivableReport.pdf';
//        $data['data']['orientation'] = 'L';
//        echo '<pre>', print_r($data);exit;
        $result = $this->invoiceexport->exportDocument('ReportExport:pdfReports', $data);
    }

    public function tdspayableReportPDFAction() {
        // Filter 1 value
        $data['from_date'] = ($this->request->get('from_date')) ? $this->request->get('from_date') : date('d/m/Y', strtotime('first day of last month'));

        // Filter 2 value
        $data['to_date'] = ($this->request->get('to_date')) ? $this->request->get('to_date') : date('d/m/Y', strtotime('last day of last month'));
        $data['url'] = $this->config->system->full_base_url . '/income-details/tdspayableReportPrint/1/' . $this->session->get("auth")['soc_id']
                . '?from_date=' . $data['from_date'] . '&to_date=' . $data['to_date'];
        $data['file_name'] = 'TDSPaybleReport.pdf';
        $result = $this->invoiceexport->exportDocument('ReportExport:pdfReports', $data);
    }

    /**
     * Members Receivable  set view
     */
    public function membersReceivableReportAction() {
        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');
        /* if(empty($data['from_date'])){
          $data['from_date'] = $this->getDisplayDate(date("Y-m-d"));
          } */

        $data['page'] = $this->request->getQuery("page", "int");
//        echo '<pre>', print_r($this->request->get());exit;
        if ($this->request->get()['save'] == 'Apply') {
            $data['page'] = 1;
        }
        $data['is_paging'] = 1;
        $dataArr = $this->invoiceexport->exportDocument('ReportExport:membersReceivableReport', $data);
        $dataArr['meta']['soc_name'] = $this->session->get("soc_name");
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $dataArr);
        $this->view->setLayout("report");
    }

    public function membersReceivableReportPrintAction($pdf = 0, $soc_id = 0) {
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setVar("config", $this->config);

        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');
        $data['is_paging'] = 1;
        $data['custom_paging'] = 15;

        if ($soc_id) {
            $conce = $this->calMultiDbFlow($soc_id);
            $this->di->setShared('dbSoc', $conce);
        } else {
            $soc_id = $this->session->get('auth')['soc_id'];
        }

        $dataArr = $this->invoiceexport->exportDocument('ReportExport:membersReceivableReport', $data);
        $dataArr['meta']['soc_name'] = ($this->session->get("soc_name")) ? $this->session->get("soc_name") : \ChsOne\Models\Society::findFirst($soc_id)->soc_name;
//        $this->print_r($dataArr['data']);
        $dataArr['data']['data']->items = array();
        for ($i = 1; $i <= $dataArr['data']['data']->last; $i++) {
            $data['page'] = $i;
            $print_data = $this->invoiceexport->exportDocument('ReportExport:membersReceivableReport', $data);
            $dataArr['data']['data']->items[($i - 1)] = $print_data['data']['data']->items;
            $dataArr['data']['data']->items[($i - 1)]['last_row'] = $print_data['data']['last_row'];
        }
//        $this->print_r($dataArr);
//        echo '<pre>', print_r($dataArr);exit;
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $dataArr);
        $this->view->setVar("pdf", $pdf);
        $this->view->setVar("soc_id", $soc_id);
        $this->view->setLayout("reportPrint");

        // For landscape orientation, set $data['data']['orientation'] = 'landscape';
    }

    public function membersReceivableReportExcelAction() {
        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');

        $data['is_paging'] = 0;
        $arrListenerdata = $this->invoiceexport->exportDocument('ReportExport:membersReceivableReport', $data);
        // Let's set replace values before taking excel export
        foreach ($arrListenerdata['data']['data']->items as $key => &$data) {
            foreach ($data as $innerKey => &$innerData) {
                if (array_key_exists($innerKey, $arrListenerdata['data']['replace_values'])) {
                    $innerData = array_key_exists($innerData, $arrListenerdata['data']['replace_values'][$innerKey]) ? $arrListenerdata['data']['replace_values'][$innerKey][$innerData] : $innerData;
                }
            }
        }
        $arrListenerdata['format'] = 'excel';
        $arrListenerdata['auth'] = $this->session->get("auth");
//        echo '<pre>', print_r($arrListenerdata);exit;
        $result = $this->invoiceexport->exportDocument('ReportExport:ReportsExcel', $arrListenerdata);
        $this->view->disable();
    }

    public function membersReceivableReportPDFAction() {
        // Filter 1 value
        $data['search_by'] = $this->request->get('search_by');
        $data['search_value'] = $this->request->get('search_value');
        $data['filter_by'] = $this->request->get('filter_by');

        $data['url'] = $this->config->system->full_base_url . '/income-details/membersReceivableReportPrint/1/' . $this->session->get("auth")['soc_id']
                . '?search_by=' . $data['search_by'] . '&search_value=' . $data['search_value'] . '&filter_by=' . $data['filter_by'];
        $data['file_name'] = 'MembersReceivableReport.pdf';
//        $data['data']['orientation'] = 'L';
//        echo '<pre>', print_r($data);exit;
        $result = $this->invoiceexport->exportDocument('ReportExport:pdfReports', $data);
    }

    public function change_themeAction($theme) {
        // echo $theme;exit;
//        $this->print_r();
        $_SESSION['theme'] = $theme;
        // $this->print_r($_SESSION['theme']);
//     	$this->session->set('theme', $theme);
//    	$this->response->redirect($this->config->system->full_base_url);
        $this->response->redirect($_SERVER['HTTP_REFERER']);
    }

    public function previewWithGenerateAction($unit_id) {
        $arrGetData = array();
        $arrData = $this->request->getQuery();

        $parts = parse_url($_SERVER['HTTP_REFERER']);
        parse_str($parts['query'], $query);

        if (!empty($arrData['_url'])) {

            $urlParam = end(explode('previewWithGenerate/', $arrData['_url']));
            $arrGetData = explode('/', $urlParam);
            if (!empty($arrGetData[0])) {
                $arrGetData['unit_id'] = json_decode($arrGetData[0]);
                unset($arrGetData[0]);
            }
            if (!empty($arrGetData[1])) {
                $arrInvoiceDate = json_decode($arrGetData[1], true);
                $arrGetData['arrInvoiceDate'] = array('start_date' => $this->getDisplayDate($arrInvoiceDate['start_date']), 'end_date' => $this->getDisplayDate($arrInvoiceDate['end_date']), 'bill_date' => $this->getDisplayDate($arrInvoiceDate['bill_date']));
                unset($arrGetData[1]);
            }
            if (!empty($arrGetData[2])) {
                $arrGetData['hideGenerate'] = (strtolower($arrGetData[2]) == 'unit-preview') ? true : false;
                $arrGetData['unit_name'] = ucwords(str_replace('-', '/', $arrGetData[2]));
                unset($arrGetData[2]);
            }
            $arrGetData['url'] = $this->config->system['full_base_url'] . "income-details/previewInvoice/" . $urlParam;
            $arrGetData['page'] = $query['page'];
            $arrGetData['unit_name'] = (empty($arrGetData['unit_name'])) ? 'Unit Preview' : $arrGetData['unit_name'];
        }
        if (empty($arrGetData)) {
            $this->session->set("err_rule", 'Due to incorrect input, Unable to generate invoice');
            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember?page=" . $query['page']);
        }
        $this->view->pick('incomedetails/previewinvoice');
        $this->view->setVar("config", $this->config);
        $this->view->setVar("arrGetData", $arrGetData);
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
    }

    /**
     * Members Invoice Detail
     */
    public function membersInvoiceDetailReportAction() {
        $arrData['is_paging'] = 1;
        $arrData['auth'] = $this->auth;
        $arrData['soc_name'] = $this->session->get('soc_name');
        $arrData['arrQueryData'] = $this->request->getQuery();
        if ($this->request->get()['save'] == 'Apply') {
            $arrData['arrQueryData']['page'] = 1;
        }
        
        $arrData['arrQueryData']['invoice_date'] = (!empty($arrData['arrQueryData']['invoice_date'])) ? $this->getDisplayDate($arrData['arrQueryData']['invoice_date']) : $this->getCurrentDate('display');
        $arrIncomeAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getIncomeAccountDetails', array('soc_id' => $arrData['auth']['soc_id'], 'arrAccountType' => array('member')));
        $arrInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberInvoiceDueDetail', array('soc_id' => $arrData['auth']['soc_id'], 'invoice_date' => $arrData['arrQueryData']['invoice_date']));
        $arrMemberInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getFormattedMemberInvoiceDueDetail', array('soc_id' => $arrData['auth']['soc_id'], 'arrInvoiceDetail' => $arrInvoiceDetail, 'arrIncomeAccountDetail' => $arrIncomeAccountDetail));
        $arrData['arrReportResult'] = array('header' => array_flip($arrMemberInvoiceDetail['arrMemberInvoiceDetailColumn']), 'data' => $arrMemberInvoiceDetail['arrMemberInvoiceDetailRow'], 'summary' => $arrMemberInvoiceDetail['arrMemberInvoiceSummary']);
        
        $arrReportHtml = $this->invoiceexport->exportDocument('ReportExport:getMemberInvoiceDetail', $arrData);
        $arrReportHtml['meta']['soc_name'] = $this->session->get("soc_name");
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $arrReportHtml);
        $this->view->setLayout("report");
    }

    /**
     * Parking ALLOTTMENT REPORT EXPORT
     */
    public function membersInvoiceDetailReportExportAction($report, $pdf = 0, $soc_id = 0) {
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setVar("config", $this->config);
        $arrData['arrQueryData'] = $this->request->getQuery();
        $arrData['arrQueryData']['invoice_date'] = (!empty($arrData['arrQueryData']['invoice_date'])) ? $arrData['arrQueryData']['invoice_date'] : $this->getCurrentDate('display');

        $arrData['is_paging'] = 0;
        if ($soc_id) {
            $conce = $this->calMultiDbFlow($soc_id);
            $this->di->setShared('dbSoc', $conce);
            $arrData['auth']['soc_id'] = $soc_id;
            $arrData['soc_name'] = \ChsOne\Models\Society::findFirst($soc_id)->soc_name;
        } else {
            $soc_id = $this->session->get("auth")['soc_id'];
            $arrData['auth'] = $this->auth;
            $arrData['soc_name'] = $this->session->get('soc_name');
        }

        if ($report != 'pdf') {
            $arrIncomeAccountDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getIncomeAccountDetails', array('soc_id' => $arrData['auth']['soc_id'], 'arrAccountType' => array('member')));
            $arrInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getMemberInvoiceDueDetail', array('soc_id' => $arrData['auth']['soc_id'], 'invoice_date' => $arrData['arrQueryData']['invoice_date']));
            $arrMemberInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getFormattedMemberInvoiceDueDetail', array('soc_id' => $arrData['auth']['soc_id'], 'arrInvoiceDetail' => $arrInvoiceDetail, 'arrIncomeAccountDetail' => $arrIncomeAccountDetail));
            $arrData['arrReportResult'] = array('header' => array_flip($arrMemberInvoiceDetail['arrMemberInvoiceDetailColumn']), 'data' => $arrMemberInvoiceDetail['arrMemberInvoiceDetailRow'], 'summary' => $arrMemberInvoiceDetail['arrMemberInvoiceSummary']);
        }
//        /print_r();
        if ($report == 'print') {
            $arrData['is_paging'] = 1;
            $arrData['custom_paging'] = 8;
            $dataArr = $this->invoiceexport->exportDocument('ReportExport:getMemberInvoiceDetail', $arrData);
            //echo '<pre>';//print_r($dataArr);exit;

            $dataArr['data']['data']->items = array();
            for ($i = 1; $i <= $dataArr['data']['data']->last; $i++) {
                $arrData['arrQueryData']['page'] = $i;
                $print_data = $this->invoiceexport->exportDocument('ReportExport:getMemberInvoiceDetail', $arrData);
                $dataArr['data']['data']->items[($i - 1)] = $print_data['data']['data']->items;
                $dataArr['data']['data']->items[($i - 1)]['last_row'] = $print_data['data']['last_row'];
            }
            //print_r($dataArr);exit;
            //$dataArr['data']['orientation'] = 'portrait';
            $this->view->setVar("data", $dataArr);
            $this->view->setVar("pdf", $pdf);
            $this->view->setVar("soc_id", $soc_id);
            $this->view->setLayout("reportPrint");
        } elseif ($report == 'excel') {
            $dataArr = $this->invoiceexport->exportDocument('ReportExport:getMemberInvoiceDetail', $arrData);
            // Let's set replace values before taking excel export
            foreach ($dataArr['data']['data']->items as $key => &$data) {
                foreach ($data as $innerKey => &$innerData) {
                    if (array_key_exists($innerKey, $dataArr['data']['replace_values'])) {
                        $innerData = array_key_exists($innerData, $dataArr['data']['replace_values'][$innerKey]) ? $arrListenerdata['data']['replace_values'][$innerKey][$innerData] : $innerData;
                    }
                }
            }
            $dataArr['format'] = 'excel';
            $dataArr['auth'] = $this->session->get("auth");
            $result = $this->invoiceexport->exportDocument('ReportExport:ReportsExcel', $dataArr);
            $this->view->disable();
        } elseif ($report == 'pdf') {
            unset($arrData['arrQueryData']['_url']);
            $data['url'] = $this->config->system->full_base_url . '/income-details/membersInvoiceDetailReportExport/print/1/' . $this->session->get("auth")['soc_id']
                    . '?' . http_build_query($arrData['arrQueryData']);
            $data['file_name'] = 'MemberInvoiceDetail.pdf';
//            /print_r($data);exit;
            $result = $this->invoiceexport->exportDocument('ReportExport:pdfReports', $data);
        }

        // For landscape orientation, set $data['data']['orientation'] = 'landscape';
    }
    
    /**
     * Members Unit Statement
     */
    public function membersUnitStatementReportAction() {
        $arrData['is_paging'] = 1;
        $arrData['auth'] = $this->auth;
        $arrData['soc_name'] = $this->session->get('soc_name');
        $arrData['arrQueryData'] = $this->request->getQuery();
        if ($this->request->get()['save'] == 'Apply') {
            $arrData['arrQueryData']['page'] = 1;
        }
        
        $arrUnitStatementDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitStatementDetail', array('soc_id'=>$arrData['auth']['soc_id'], 'unit_id'=>$arrData['arrQueryData']['unit'], 'from_date'=>$arrData['arrQueryData']['from_date'], 'to_date'=>$arrData['arrQueryData']['to_date']));
        $arrData['arrReportResult'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:formatUnitStatementDetailReport', array('arrUnitStatementDetail'=>$arrUnitStatementDetail));
        $arrReportHtml = $this->invoiceexport->exportDocument('ReportExport:getUnitStatementReportDetail', $arrData);
        $arrReportHtml['meta']['soc_name'] = $this->session->get("soc_name");
        $this->view->setVar("config", $this->config);
        $this->view->setVar("data", $arrReportHtml);
        $this->view->setLayout("report");
    }
    
    /**
     * Members Unit Statement REPORT EXPORT
     */
    public function membersUnitStatementReportExportAction($report,$pdf = 0, $soc_id = 0) {
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
        $this->view->setVar("config", $this->config);
        $arrData['arrQueryData'] = $this->request->getQuery();
        
        $arrData['is_paging']     = 0;
        if($soc_id) 
        {
            $conce = $this->calMultiDbFlow($soc_id);
            $this->di->setShared('dbSoc', $conce);
            $arrData['auth']['soc_id'] = $soc_id;
            $arrData['soc_name'] = \ChsOne\Models\Society::findFirst($soc_id)->soc_name;
        } 
        else
        {
            $soc_id = $this->session->get("auth")['soc_id'];
            $arrData['auth'] = $this->auth;
            $arrData['soc_name'] = $this->session->get('soc_name');
        }
        
        if($report != 'pdf') 
        {
            $arrUnitStatementDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitStatementDetail', array('soc_id'=>$arrData['auth']['soc_id'], 'unit_id'=>$arrData['arrQueryData']['unit'], 'from_date'=>$arrData['arrQueryData']['from_date'], 'to_date'=>$arrData['arrQueryData']['to_date']));
            $arrData['arrReportResult'] = $this->incometrackerevent->incometracker('MemberIncomeDetail:formatUnitStatementDetailReport', array('arrUnitStatementDetail'=>$arrUnitStatementDetail));
        }
//        /print_r();
        if($report == 'print')
        {
            $arrData['is_paging']     = 1;
            $arrData['custom_paging'] = 12;
            $dataArr = $this->invoiceexport->exportDocument('ReportExport:getUnitStatementReportDetail', $arrData);
            //echo '<pre>';//print_r($dataArr);exit;
            
            $dataArr['data']['data']->items = array();
            for($i = 1; $i <= $dataArr['data']['data']->last; $i++) {
                $arrData['arrQueryData']['page'] = $i;
                $print_data = $this->invoiceexport->exportDocument('ReportExport:getUnitStatementReportDetail', $arrData);
                $dataArr['data']['data']->items[($i-1)] = $print_data['data']['data']->items;
                $dataArr['data']['data']->items[($i-1)]['last_row'] = $print_data['data']['last_row'];
            }
            //print_r($dataArr);exit;
            //$dataArr['data']['orientation'] = 'portrait';
            $this->view->setVar("data", $dataArr);
            $this->view->setVar("pdf", $pdf);
            $this->view->setVar("soc_id", $soc_id);
            $this->view->setLayout("reportPrint");
        }
        elseif($report == 'excel')
        {
            $dataArr = $this->invoiceexport->exportDocument('ReportExport:getUnitStatementReportDetail', $arrData);
            // Let's set replace values before taking excel export
            foreach ($dataArr['data']['data']->items as $key => &$data) {
                foreach ($data as $innerKey => &$innerData) {
                    if(array_key_exists($innerKey, $dataArr['data']['replace_values'])) {
                        $innerData = array_key_exists($innerData, $dataArr['data']['replace_values'][$innerKey]) ? $arrListenerdata['data']['replace_values'][$innerKey][$innerData] : $innerData;
                    }
                }
            }
            $dataArr['format'] = 'excel';
            $dataArr['auth']   = $this->session->get("auth");
            $result = $this->invoiceexport->exportDocument('ReportExport:ReportsExcel', $dataArr);
            $this->view->disable();
        }
        elseif($report == 'pdf')
        {
            unset($arrData['arrQueryData']['_url']);
            $data['url'] = $this->config->system->full_base_url . '/income-details/membersUnitStatementReportExport/print/1/' . $this->session->get("auth")['soc_id'] 
                    . '?'.http_build_query($arrData['arrQueryData']);
            $data['file_name'] = 'MemberUnitStatement.pdf';
//            /print_r($data);exit;
            $result = $this->invoiceexport->exportDocument('ReportExport:pdfReports', $data);
        }
        
        // For landscape orientation, set $data['data']['orientation'] = 'landscape';
    }

    public function checkViewAction() {

        $this->view->pick('incomedetails/previewinvoice');
        $this->view->setVar("config", $this->config);
        $this->view->disableLevel(View::LEVEL_MAIN_LAYOUT);
    }

    /**
     * Returns invoice due date.
     * 
     * @method getInvoiceDueDateAction
     * @access public
     */
    public function getInvoiceDueDateAction() {
        $auth = $this->session->get("auth");
        $bill_date = trim($this->request->getPost("bill_date", "striptags"));
        if (!empty($bill_date)) {
            $dueDate = $this->incometrackerevent->incometracker('InvoiceGenerator:getInvoiceDueDate', array('soc_id' => $auth['soc_id'], 'bill_date' => $bill_date));
            if (!empty($dueDate)) {
                echo json_encode($this->getDisplayDate($dueDate));
                exit();
            }
        }
        echo 0;
        exit;
    }
    
    /**
     * 
     * @param type $unit_id
     * @return type
     */
    public function memberInvoicelistAction($unit_id = '') {
        
//        $lastpage = $this->request->getQuery("page", "int");
//        if(empty($lastpage) && strstr($_SERVER['HTTP_REFERER'], '/incomemember'))
//        {
//            $lastpage = array_pop(explode('page=', $_SERVER['HTTP_REFERER']));
//            $lastpage = !empty($lastpage) ? $lastpage : 1;
//        }
        if (!empty($unit_id) && is_numeric($unit_id)) {
            $this->setActionUrl("income-details/memberInvoicelist/$unit_id", "income-details/memberInvoicelist/$unit_id");
//            $this->setSearchView("income-details/memberInvoicelist/$unit_id", ["inv" => "inv"]);
            $this->setSearchView("income-details/memberInvoicelist/$unit_id", ["invoice_no" => "invoice_no" 
            ]);
            $this->setFilterView("income-details/memberInvoicelist/$unit_id", [
                            "payment_status"=>["paid" => "paid", "unpaid" => "unpaid", "partialpaid" => "partialpaid"]
                    ]
            );

            if($this->request->isPost()){
                $searchArr  =   $this->request->getPost();
//                print_r($searchArr); exit;
                $searchMemberFilterPost['search_by'] = $searchArr['search_by'];
                $searchMemberFilterPost['search_key'] = $searchArr['search_key'];
                $searchMemberFilterPost['filters'] = $searchArr['filters'];
                $this->setPostSearchFilterArr("income-details/memberInvoicelist/$unit_id", $searchMemberFilterPost);

            }
//            $arrDataListener['searchMemberInvoicelistQuery']  = \ChsOne\Helper\SearchHelper::getSearchQuery("income-details/memberInvoicelist/$unit_id");
//            $arrDataListener['filterMemberInvoicelistQuery']  = \ChsOne\Helper\SearchHelper::getFilterQuery("income-details/memberInvoicelist/$unit_id");


            $auth = $this->session->get('auth');
            $arrIncomeInvoiceDetail = array();


            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $unit_id;
            $arrDataListener['show_cancelled_invoice'] = 1;
//            if (!empty($this->request->getPost())) {
//                $this->view->setVar('search_arr', $this->request->getPost());
//                $arrDataListener['searchData'] = $this->request->getPost();
//            }
            $arrCreditDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getCreditAmountForIndividuals', array('data' => array('soc_id' => $auth['soc_id'], 'account_id' => $unit_id, 'account_context' => 'unit', 'bill_date' => $this->getCurrentDate('database'), 'bill_type' => 'maintenance'))); //get advance details
            $creditAmount = (isset($arrCreditDetail['remaining_amount']) && !empty($arrCreditDetail['remaining_amount'])) ? number_format(round($arrCreditDetail['remaining_amount'], 3), 2, '.', '') : number_format(0, 2, '.', '');
            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceDetail', $arrDataListener); //get all Unit details
            $arrAllInvoiceNumber = $this->incometrackerevent->incometracker('MemberIncomeDetail:getAllInvoiceNumber', $arrDataListener); //get all Unit details
            //echo '<pre>';//print_r($arrIncomeInvoiceDetail);exit;

            if(empty($arrIncomeInvoiceDetail)){
                $unitDetls = Units::findFirst('soc_id = "' . $auth['soc_id'] . '" AND unit_id = "' . $unit_id . '"')->toarray();
            }

            //Get receipt data
            $number_page = !empty($this->request->getQuery("page", "int")) ? $this->request->getQuery("page", "int") : 1;
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'full_list' => true);
            $arrPaymentTrackerDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerList', $arrPaymentTrackerListener); // get all Unit details
//            echo '<pre>'; print_r($arrPaymentTrackerDetail); exit;
//            $paginator = new Paginatorbyquery(array(
//                "builder" => $objQueryBuiler,
//                "limit" => PAGE_NUMBER,
//                "page" => $number_page
//            ));
//            $page = $paginator->getPaginate();
//            $arrPaymentTrackerDetail = $page->items->toArray();
            $arrInvoicePaymentTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPaymentTrackerListedData', array('arrPaymentTrackerDetail' => $arrPaymentTrackerDetail)); //get all Unit details

            $this->view->setVar("arrInvoicePaymentTracker", $arrInvoicePaymentTracker);
        } else {
            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember");
        }
        
        $memberInvoiceUrl  =   $this->config->system->full_base_url . "income-details/memberInvoicelist/$unit_id";
        $memberReceiptsUrl  =   $this->config->system->full_base_url . "income-details/memberReceiptslist/$unit_id";
//        /echo '<pre>';print_r($arrCreditDetail);exit;
        $this->view->setVar("creditAmount", $creditAmount);
        $this->view->setVar("lastPage", $lastpage);
        $this->view->setVar("unit_id", $unit_id);
        $this->view->setVar("memberInvoiceUrl", $memberInvoiceUrl);
        $this->view->setVar("memberReceiptsUrl", $memberReceiptsUrl);
        $this->view->setVar("arrAllInvoiceNumber", $arrAllInvoiceNumber);
        $this->view->setVar("unitDetls", $unitDetls);
        $this->view->setVar("arrIncomeInvoiceDetail", $arrIncomeInvoiceDetail['unit_invoice_detail']);
        $this->view->setVar("nonCancelledInvoice", $arrIncomeInvoiceDetail['total_non_cancelled_invoice']);

        $this->view->setVar("config", $this->config);
    }
    
    /**
     * 
     * @param type $unit_id
     * @return type
     */
    
    public function memberReceiptslistAction($unit_id = '') {
        if (!empty($unit_id) && is_numeric($unit_id)) {
            $auth = $this->session->get('auth');
            $arrIncomeInvoiceDetail = array();

            $this->setActionUrl("income-details/memberReceiptslist/$unit_id", "income-details/memberReceiptslist/$unit_id");
//            $this->setSearchView("income-details/memberInvoicelist/$unit_id", ["inv" => "inv"]);
            $this->setSearchView("income-details/memberReceiptslist/$unit_id", ["paymentTracker.receipt_number" => "receipt_number", 
            ]);
            
            $arrGeneralSettingData = array('soc_id' => $auth['soc_id'], 'setting_key' => array('INCOME_PAYMENT_MODE'));
            $arrInvoiceGeneralSetting = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoiceGeneralSetting', $arrGeneralSettingData);
    //        $payment_mode   =   array();
            $payment_mode   =   explode(',',$arrInvoiceGeneralSetting[0]["setting_value"]);

            $arrPaymentMode =   array_combine($payment_mode, $payment_mode);
            
            $this->setFilterView("income-details/memberReceiptslist/$unit_id", [
    			"paymentTracker.status"=>["R" => "received", "P" => "submitted", "Y" => "cleared", "N" => "bounced", "not_received" => "not_received", "reversed" => "reversed"],
    			"paymentTracker.payment_mode"=> $arrPaymentMode,
                        "pdc" => ["pdc" => "pdc"]
                    ]
            );

            if($this->request->isPost()){
                $searchArr  =   $this->request->getPost();
//                print_r($searchArr); exit;
                $searchMemberFilterPost['search_by'] = $searchArr['search_by'];
                $searchMemberFilterPost['search_key'] = $searchArr['search_key'];
                $searchMemberFilterPost['filters'] = $searchArr['filters'];
                $this->setPostSearchFilterArr("income-details/memberReceiptslist/$unit_id", $searchMemberFilterPost);

            }
            echo $searchReceiptTrackerQuery  = \ChsOne\Helper\SearchHelper::getSearchQuery("income-details/memberReceiptslist/$unit_id");
            $filterReceiptTrackerQuery  = \ChsOne\Helper\SearchHelper::getFilterQuery("income-details/memberReceiptslist/$unit_id");

        
//        $lastpage = $this->request->getQuery("page", "int");
//        if(empty($lastpage) && strstr($_SERVER['HTTP_REFERER'], '/incomemember'))
//        {
//            $lastpage = array_pop(explode('page=', $_SERVER['HTTP_REFERER']));
//            $lastpage = !empty($lastpage) ? $lastpage : 1;
//        }
            $arrDataListener['soc_id'] = $auth['soc_id'];
            $arrDataListener['unit_id'] = $unit_id;
            $arrDataListener['show_cancelled_invoice'] = 1;
            if (!empty($this->request->getPost())) {
                $this->view->setVar('search_arr', $this->request->getPost());
                $arrDataListener['searchData'] = $this->request->getPost();
            }
            $arrCreditDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getCreditAmountForIndividuals', array('data' => array('soc_id' => $auth['soc_id'], 'account_id' => $unit_id, 'account_context' => 'unit', 'bill_date' => $this->getCurrentDate('database'), 'bill_type' => 'maintenance'))); //get advance details
            $creditAmount = (isset($arrCreditDetail['remaining_amount']) && !empty($arrCreditDetail['remaining_amount'])) ? number_format(round($arrCreditDetail['remaining_amount'], 3), 2, '.', '') : number_format(0, 2, '.', '');
            $arrIncomeInvoiceDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getUnitInvoiceDetail', $arrDataListener); //get all Unit details
            $arrAllInvoiceNumber = $this->incometrackerevent->incometracker('MemberIncomeDetail:getAllInvoiceNumber', $arrDataListener); //get all Unit details
            //echo '<pre>';//print_r($arrIncomeInvoiceDetail);exit;
            //Get receipt data
            $number_page = !empty($this->request->getQuery("page", "int")) ? $this->request->getQuery("page", "int") : 1;
            $arrPaymentTrackerListener = array('soc_id' => $auth['soc_id'], 'unit_id' => $unit_id, 'full_list' => true, 'module' => 'memberReceiptslist', 'searchReceiptTrackerQuery' => $searchReceiptTrackerQuery, 'filterReceiptTrackerQuery' => $filterReceiptTrackerQuery);
            $arrPaymentTrackerDetail = $this->incometrackerevent->incometracker('MemberIncomeDetail:getInvoicePaymentTrackerList', $arrPaymentTrackerListener); // get all Unit details
            
//            if ($arrPaymentTrackerDetail['error'] !== false) {
//                $this->session->set("err_msg", "Please enter valid search key");
//            return $this->response->redirect($this->config->system->full_base_url . "income-details/memberReceiptslist/$unit_id");
//            } else {
//                $arrPaymentTrackerDetail = $arrPaymentTrackerDetail['data'];
//            }
//
//            $search_filter  =   $this->session->get('search_filter');
//            
//            $paginator = new Paginatorbyquery(array(
//                "builder" => $arrPaymentTrackerDetail,
//                "limit" => PAGE_NUMBER,
//                "page" => $search_filter["income-details/memberReceiptslist/$unit_id"]['current_page']
//            ));
//            $page = $paginator->getPaginate();
//            $arrPaymentTrackerDetail = $page->items->toArray();
            $arrInvoicePaymentTracker = $this->incometrackerevent->incometracker('MemberIncomeDetail:getPaymentTrackerListedData', array('arrPaymentTrackerDetail' => $arrPaymentTrackerDetail)); //get all Unit details

            $this->view->setVar("arrInvoicePaymentTracker", $arrInvoicePaymentTracker);
        } else {

            return $this->response->redirect($this->config->system->full_base_url . "income-details/incomemember");
        }
        
        $memberInvoiceUrl  =   $this->config->system->full_base_url . "income-details/memberInvoicelist/$unit_id";
        $memberReceiptsUrl  =   $this->config->system->full_base_url . "income-details/memberReceiptslist/$unit_id";
//        /echo '<pre>';print_r($arrCreditDetail);exit;
        $this->view->setVar("creditAmount", $creditAmount);
        $this->view->setVar("lastPage", $lastpage);
        $this->view->setVar("unit_id", $unit_id);
        $this->view->setVar("memberInvoiceUrl", $memberInvoiceUrl);
        $this->view->setVar("memberReceiptsUrl", $memberReceiptsUrl);
        $this->view->setVar("arrAllInvoiceNumber", $arrAllInvoiceNumber);
        $this->view->setVar("arrIncomeInvoiceDetail", $arrIncomeInvoiceDetail['unit_invoice_detail']);
        $this->view->setVar("nonCancelledInvoice", $arrIncomeInvoiceDetail['total_non_cancelled_invoice']);

        $this->view->setVar("config", $this->config);
    }

}
