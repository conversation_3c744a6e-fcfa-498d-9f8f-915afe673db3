<?php

namespace ChsOne\Integration\Controllers;


use Phalcon\Mvc\User\Component;
use ChsOne\Models\User;
use ChsOne\Components\Emailer\EmailEvent;
use ChsOne\Components\Emailer\Listeners\UserMailListener;
use ChsOne\Models\Society;
use ChsOne\Models\MemberType;
//use ChsOne\User\Controllers\UsersController;
use ChsOne\Components\IncomeTracker\IncometrackerEvent;
//use ChsOne\Users\Controllers as UserssController;
//use ChsOne\SocietySetup\Controllers\ControllerBase;
use Phalcon\Paginator\Adapter\QueryBuilder as Paginatorbyquery;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;
use ChsOne\Forms\StaffTypeForm,
    ChsOne\Forms\StaffMemberSignupForm;
    
// use ChsOne\Components\Staff\Listeners\StaffSettingpageListener;
use ChsOne\Components\Staff\StaffsettingEvent;
use ChsOne\Components\Setup\SetupEvent;

use ChsOne\Components\Members\MemberEvent;
use ChsOne\Components\Members\Listeners\MemberListener;
use ChsOne\Components\Units\UnitEvent;
use Phalcon\Db\Adapter\Pdo\Mysql as Connection;

class IntegrationController extends BaseController {

    use \ChsOne\Components\Traits\DateFormatTrait;

    use \ChsOne\Components\Traits\CrossPortalTrait;

    public function initialize() {
        global $di;
        global $config;
        $this->config = $config;
        $this->status_code = $this->config->api_response_status_codes;
        $this->email = new \ChsOne\Components\Emailer\EmailEvent($this->config);
        $this->email->addListener('Email', '\ChsOne\Components\Emailer\Listeners\UserMailListener');
        $this->addmember = new \ChsOne\Components\Members\MemberEvent($this->config);
        $this->addmember->addListener('Member', '\ChsOne\Components\Members\Listeners\MemberListener');
        $this->email = new \ChsOne\Components\Emailer\EmailEvent($this->config);
        $this->email->addListener('Email', '\ChsOne\Components\Emailer\Listeners\UserMailListener');
        $this->resource = $di->getShared('resource');
        // $this->resource->setTokenKey('token');
        $this->incometrackerevent = new IncometrackerEvent($config);
        $this->incometrackerevent->addListener('MemberIncomeDetail', 'ChsOne\Components\IncomeTracker\Tracking\Listeners\MemberIncomeListener');
        $this->incometrackerevent->addListener('accountListener', 'ChsOne\Components\IncomeTracker\Tracking\Listeners\InvoiceAccountListener');
        $this->incometrackerevent->addListener('commonBilling', 'ChsOne\Components\IncomeTracker\Tracking\Listeners\CommonBillingListener');


        $this->staff = new StaffsettingEvent($config);
        $this->staff->addListener('saveStaffMember', 'ChsOne\Components\Staff\Listeners\StaffSettingpageListener');
       
        $this->setup = new SetupEvent($config);
        $this->setup->addListener('setupsetting', 'ChsOne\Components\Setup\Listeners\SetuppageListener');

        $this->addunit = new \ChsOne\Components\Units\UnitEvent($this->config);
        $this->addunit->addListener('Unit', '\ChsOne\Components\Units\Listeners\UnitListener');

        $this->headers = $this->request->getHeaders();

    }

    

    /**
     * 
     * @global type $di
     * @return type
     * @throws \League\OAuth2\Server\Exception\MissingAccessTokenException
     * @throws \League\OAuth2\Server\Exception\InvalidAccessTokenException
     * 
     */
    public function postgenerateaccesstokenAction() {
        global $di;
        global $config;
        global $constants;
        $this->oauth = $di->getShared('oauth');
        try {
            if (!$this->request->isPost()) {
                throw new \Exception("Invalid request type.");
            }

            $soc_id = $this->request->getPost('soc_id');
            $grant_type = $this->request->getPost('grant_type');
            if ($grant_type == '') {
                throw new \League\OAuth2\Server\Exception\MissingAccessTokenException('Grant type is missing.');
            }
            if ($grant_type == 'password') {
                $validation = new \ChsOne\Components\Traits\ResidentApiTraits\UserValidationsTrait();
                $validationError = $validation->generateaccesstoken($this->request->getPost());
                if (count($validationError)) {
                    return $this->customSetErrorResponse($validationError, 422);
                }
                $api_key = $this->request->getPost('api_key'); //exit;
                $this->societyPresence($soc_id);
                if (!isset($api_key) || empty($api_key)) {

                    throw new \League\OAuth2\Server\Exception\MissingAccessTokenException('API key is missing.Please provide valid API key');
                } else {

                    $validity = Society::count('api_key = "' . $api_key . '"');
                    //echo $validity;exit;
                    $api_key_present = Society::count('api_key = "' . $api_key . '" AND soc_id = "' . $soc_id . '"');

                    //exit;
                    if ($validity > 0 && $api_key_present < 1) {
                        throw new \League\OAuth2\Server\Exception\ClientException('API key is expired.');
                    }

                    if ($api_key_present < 1) {
                        throw new \League\OAuth2\Server\Exception\InvalidAccessTokenException('API key is invalid.Please provide valid API key');
                    }
                }
            }
            if (!isset($grant_type) || empty($grant_type)) {
                throw new \League\OAuth2\Server\Exception\MissingAccessTokenException('Please provide valid grant type.');
            }//echo '11111';exit;
            $params = $this->oauth->getParam(array('client_id', 'client_secret', 'soc_id'));

            $access = json_encode($this->oauth->getGrantType($grant_type)->completeFlow($params));
            $access = json_decode($access);
            //print_r($access);exit;
            if ($grant_type == 'password') {
                $ownerType = 'user';
                $loginController = new UserController();
                $owner = $this->loginAction($this->request->getPost('username'), $this->request->getPost('password'), $app_token, $soc_id);
                // print_r($owner);exit();
                if ($owner) {

                    $condition = 'user_id = ' . $owner;
                    $userData = \ChsOne\Models\User::findFirst(array('conditions' => $condition));
                    //                    print_r($userData->toArray());exit();
                    if (time() > $userData->app_token_expires) {
                    //                        $userDetails = User::findfirst('soc_id = "'.$userData->soc_id.'" AND user_id = "'.$userData->user_id.'"');
                        $data = $this->generateAppToken($userData->user_name);
                        $userData->app_token = $data['app_token'];
                        $userData->app_token_expires = $data['accessTokenExpires'];
                    //                        print_r($userDetails->toArray());exit();
                        $userData->update();
                    }
                    //                    echo $userData->user_id;exit();
                    $memberModel = new \ChsOne\Models\Member();
                    $members = $memberModel->getMemberData($userData->user_id);
                    $units = $memberModel->getUserUnitMembers(['user_id' => $userData->user_id, 'soc_id' => $userData->soc_id]);
                    $ownerId = $owner;
                    $userData = $userData->toArray();
                    $soc_name = Society::findFirst(array('conditions' => 'soc_id = ' . $userData['soc_id'], 'columns' => 'soc_name'));
                    $userData['soc_name'] = $soc_name->soc_name;
                    unset($userData['password']);
                    $userData['make_public'] = $members[0]['phone_no_privacy'];
                    $userData['members'] = $members;
                    $userData['units'] = $units->toArray();
                    $userData['user_profile_photo'] = $config->s3server_details->s3_security_protocol . $config->s3server_details->bucket_name . '.s3.amazonaws.com/' . $userData["soc_id"] . '/avatar/' . $userData['user_id'] . '/' . $userData['user_profile_photo'];
                    $expense_limits = \ChsOne\Models\ExpenseLimitRanges::find()->toArray();
                    $settings['society_settings'] = SocietyPreferences::find()->toArray();

                    $settings['expense_limits'] = $expense_limits;
                } else {
                    throw new \League\OAuth2\Server\Exception\InvalidAccessTokenException('User Not Found.');
                }
            } elseif ($grant_type == 'client') {
                $ownerType = 'client';
                $ownerId = $this->request->getPost('client_id');
            }
            $message = "Welcome";
            $data = (array) $access;
            return $this->setSuccessResponse($message, $data);
        } catch (\League\OAuth2\Server\Exception\OAuth2Exception $e) {
            return $this->setErrorResponse($e);
        } catch (\Exception $e) {
            return $this->setErrorResponse($e);
        }
    }
    public function poststaffsaveAction() {
        try {
            // Authenticate Token
            $token = $this->request->getPost('token');
            $this->resource->setTokenKey('token');
            $this->resource->isValid(null,$token);
            // $this->connectDB($this->request->getPost('unit_id'));
            if (!$this->request->isPost()) {
                throw new \Exception("Invalid request type.");
            }

            // check if unit exists
            
            $soc_id = $this->request->getPost()['unit_id'];
            if (!isset($soc_id) || $soc_id =='' ){
                throw new \Exception("society id not provided.");
            }
            $objSocietyMaster = \ChsOne\Models\SocDbDetails::findFirst('soc_id IN ('.$soc_id.') AND status = 1 ');
            if(empty($objSocietyMaster))
            {
                throw new \Exception("society not found.");
            } 
            
            $this->connectDB($this->request->getPost('unit_id'));
            // prepare data
            
            $data['arrPostData']['staff_first_name']=$this->request->getPost('first_name');
            $data['arrPostData']['staff_last_name']=$this->request->getPost('last_name');
            $data['arrPostData']['staff_contact_number']=$this->request->getPost('mobile');
            $data['arrPostData']['staff_address_1']=$this->request->getPost('city');
            $data['arrPostData']['staff_gender']=$this->request->getPost('gender');
            $data['arrPostData']['staff_dob']=$this->request->getPost('staff_dob');
            $data['arrPostData']['staff_qualification']='';
            $data['arrPostData']['staff_skill']='';
            $data['arrPostData']['staff_lang_iso_639_3']='';
            $data['arrPostData']['access_id']='';
            $data['arrPostData']['staff_note']='';
            $data['arrPostData']['staff_badge_number']=$this->request->getPost('staff_badge_number');
            $data['arrPostData']['staff_image']='';
            $data['arrPostData']['staff_email_id']='';
            $data['arrPostData']['staff_id']=$this->request->getPost('staff_id');
            $data['auth']['soc_id']=$this->request->getPost('unit_id');

            // prepare data for category check task
            $cat_staff['soc_id']=$this->request->getPost('unit_id');
            $cat_staff['staff_member_type_name']='vizlog_staff';
            
            $cat_res = $this->staff->staffmainsetting('saveStaffMember:saveStaffCategory',$cat_staff);
            if($cat_res['status']=='error')
            {
                $message = "could not add category";
                 $this->setErrorResponse($message);
            }
            //add category id to data  array.
            $data['arrPostData']['staff_type_id']=$cat_res['category_id'];

            
            // check form validataion
            $form = new StaffMemberSignupForm(NULL, $opt);
            if ($form->isValid($data['arrPostData']) == false) {
                $message = "Form is not valid";
                return $this->setErrorResponse($message);
            }

            
            $save_res = $this->staff->staffmainsetting('saveStaffMember:saveStaffMember',$data);
            
            if($save_res['status']=='error')
            {
                $message = "could not add staff";
                //rollback all transactions
                // $this->soc_db_w->rollback();
                return $this->setErrorResponse($message);
            }
            // add files uploded if avaialable
            if ($this->request->hasFiles() == true) {
                $uploads = $this->request->getUploadedFiles();
                $data_image['uploads']=$uploads;
                $data_image['staff_id']=$save_res['staff_id'];
                $data_image['soc_id']=$data['auth']['soc_id'];
                $data_image['insert_type']=$save_res['insert_type'];
                $staff_image_res = $this->staff->staffmainsetting('saveStaffMember:saveStaffImage',$data_image);
                if($staff_image_res['status']=='error')
                {
                    $message = "could not add staff Image";
                    // $this->soc_db_w->rollback();
                    return $this->setErrorResponse($message);
                }
            }

            $data['arrPostData']['insert_type']=$save_res['insert_type'];
            $data['arrPostData']['staff_id']=$save_res['staff_id'];
            // add ledger
            $staff_ledger_res = $this->staff->staffmainsetting('saveStaffMember:createStaffLedger',$data);
            if($staff_ledger_res['status']=='error')
            {
                $message = "could not add ledger";
                // $this->soc_db_w->rollback();
                return $this->setErrorResponse($message);
            }

            //commit transaction 
            // $this->soc_db_w->commit();
            $message = "staff added successfully";
            $data = array('staff_id'=>$save_res['staff_id']);
            return $this->setSuccessResponse($message, $data);
        }catch (\League\OAuth2\Server\Exception\OAuth2Exception $e) {
            return $this->setErrorResponse($e);
        } catch (\Exception $e) {
            return $this->setErrorResponse($e);
        }

    }

    public function postsavevizlogdataAction()
    {
        global $di;
        try {
            // Authenticate Token
            $token = $this->request->getPost('token');
            if(!isset($token))
            {
                $token=$this->headers['Token'];
            }
            $this->resource->setTokenKey('token');
            $this->resource->isValid(null,$token);
            if (!$this->request->isPost()) {
                throw new \Exception("Invalid request type.");
            }
            $data=$this->request->getPost()['data'];
            $soc_id =  $this->request->getPost()['soc_id'];
            $data=json_decode($data,true);
            $di->remove('soc_db_r');
            $di->remove('soc_db_w');
            $this->setConn($soc_id);
            if(!empty($di->getShared("soc_db_r"))) 
            {
                if($this->request->getPost()['request_for']=="members")
                {
                    
                    if(sizeof($data['members'])<1)
                    {
                        throw new \Exception('Empty Data');
                    }
                    $result=$this->_saveMemberFromVizlog($data);
                    if($result['error']==true)
                    {   $error_msg="";
                        foreach ($result['error_msg'] as $value) {
                            $error_msg.=$value.". ";
                        }
                        throw new \Exception(strip_tags($error_msg));
                    }
                    else
                    {
                        return $this->setSuccessResponse("Members added successfully", $result);
                    }
                }
                else if($this->request->getPost()['request_for']=="units")
                {

                    if(sizeof($data['units'])<1)
                    {
                        throw new \Exception('Empty Data');
                    }
                    $result=$this->_saveUnitsFromVizlog($data);
                    if($result['error']==true)
                    {  
                        throw new \Exception(strip_tags($result['error_msg']));
                    }
                    else
                    {
                        return $this->setSuccessResponse("Units added successfully", $result);
                    }
                }
                else if($this->request->getPost()['request_for']=="buildings")
                {
                    if(sizeof($data['buildings'])<1)
                    {
                        throw new \Exception('Empty Data');
                    }
                    $result=$this->_saveBuildingsFromvizlog($data['buildings']);
                    if($result['error']==true)
                    {
                        $error_msg="";
                        foreach ($result['error_msg'] as $value) {
                            $error_msg.=$value.". ";
                        }
                        throw new \Exception(strip_tags($result['error_msg']));
                    }
                    else
                    {
                        return $this->setSuccessResponse("Building added successfully", $result);
                    }
                }
                $di->remove('soc_db_r');
                $di->remove('soc_db_w');
            }
        }catch (\League\OAuth2\Server\Exception\OAuth2Exception $e) {
            return $this->setErrorResponse($e);
        } catch (\Exception $e) {
            return $this->setErrorResponse($e);
        }
    }
    private function _saveMemberFromVizlog($data)
    {
        try{
            $formatedData =$this->setup->setupmainsetting('setupsetting:formatMemberData',$data['members']);
            if(isset($formatedData['error']) && $formatedData['error']==true)
            {
                // throw new \Exception($formatedData['error_msg']);
                $result['error']=true;
                $result['error_msg'][]=$formatedData['error_msg'];
                return $result;
            }
            $result=array();
            foreach ($formatedData as $fd) {
               $saveResult = $this->addmember->manageMember('Member:saveMember', $fd);
               if(isset($saveResult['result']) && $saveResult['result']==0)
               {
                    unset($saveResult['result']);
                    $result['error']=true;
                    foreach ($saveResult as $key => $value) {
                        $result['error_msg'][]="For member_id ".$fd['arrPost']['vizlog_member_id'].":".$value;
                    }

               }
               else
               {
                    $result['error']=false;
                    $result['members'][]=array(
                                        "vizlog_member_id"=>$fd['arrPost']['vizlog_member_id'],
                                        "chsone_member_id"=>$saveResult
                                        );
               }
            }
            return $result;
            // return $this->setSuccessResponse("Members added successfully", $result);
        }catch (\Exception $e) {
            return $this->setErrorResponse($e);
        }
    }

    private function _saveUnitsFromVizlog($data)
    {
        try{
            $result=array();
            $formatedData =$this->setup->setupmainsetting('setupsetting:formatUnitsData',$data);
            $vizlogIds=$formatedData['vizlogIds'];
            unset($formatedData['vizlogIds']);
            if(isset($formatedData['error']) && $formatedData['error']==true)
            {
                $result['error']=true;
                $result['error_msg']="Format error";
                return $result;
            }
            $saveResult = $this->addunit->manageUnit('Unit:saveBulkUnit', $formatedData);
            if($saveResult['flag']==0)
            {
                $result['error']=true;
                $result['error_msg']="Could Not Save Units";
            }
            else
            {
                $result['error']=false;
                $result['units']=$saveResult['unit_mapping'];
            }
            
            return $result;
        }catch (\Exception $e) {
            return $this->setErrorResponse($e);
        }
    }

    private function _saveBuildingsFromvizlog($data)
    {
        try{
            $formatedData =$this->setup->setupmainsetting('setupsetting:formatBuildingData',$data);
            if(empty($formatedData))
            {
                $result['error']=true;
                $result['error_msg']="Data Format issue."; 
            }
            foreach ($formatedData as $fd) {
                $saveResult = $this->addunit->manageUnit('Unit:saveBuilding', $fd);
                if($saveResult['error']==true)
                {
                    $result['error']=true;
                    $result['error_msg'][]="For building_id ".$fd['vizlog_building_id'].":".$saveResult['error_msg'];
                }
                else
                {
                    $result['buildings'][]=$saveResult['building_mapping'];
                }
            }
            return $result; 
        }catch (\Exception $e) {
            return $this->setErrorResponse($e);
        }
    }
    public function setConn($soc_id)
    {
        global $config, $di;
        $objSocietyMaster = \ChsOne\Models\SocDbDetails::findFirst('soc_id IN ('.$soc_id.') AND status = 1 ');

        if(!empty($objSocietyMaster))
        {
            $soc_db = $objSocietyMaster->toArray();
            
            $di->setShared('soc_db_w', function() use($di, $soc_db) {
                
                    $arrSocdbslave = $di->getShared("session")->get("soc_db_details");
                    $connection_r = new Connection(array(
                        "host" => $soc_db['soc_db_server_ip_s'],
                        "username" => $soc_db['soc_db_username_s'],
                        "password" => $this->dncAction($soc_db['soc_db_pwd_s']),
                        "dbname" => $soc_db['soc_db_name_s']
                    ));
                    
                    return $connection_r;
                });
                
            $di->setShared('soc_db_r', function() use($di, $soc_db) {
                $arrSocdbslave = $di->getShared("session")->get("soc_db_details");
                $connection_w = new Connection(array(
                    "host" => $soc_db['soc_db_server_ip_m'],
                    "username" => $soc_db['soc_db_username_m'],
                    "password" => $this->dncAction($soc_db['soc_db_pwd_m']),
                    "dbname" => $soc_db['soc_db_name_m']
                ));

                return $connection_w;
            });
        }
    }

    public function dncAction($id) {

            $replacer = array(
                1 => 'chsOne',
                2 => 'CHSoNE',
                3 => 'cHsOnE',
                4 => 'CHS1',
                5 => 'CHSONe'
            );

            $pwd = array(
                1 => array('pass'   => 'PaWord'),
                2 => array('ps'     => 'Pd'),
                3 => array('pw'     => 'Passd'),
                4 => array('pword'  => 'PWord'),
                5 => array('paswrd' => 'Pasd')
            );

            if($id == '') {
                echo 'Please provide soc_id';
                exit;
            }
            $str      = pack('H*', $id);
            $indexer  = (pack('H*', $id)[0]);
            $replacer = $replacer[$indexer]; 


            $result = str_replace($replacer, "", $str);

            reset($pwd[$indexer]);
            $first_key = key($pwd[$indexer]);

            $result = str_replace($pwd[$indexer][$first_key], $first_key . '_', $result);
            return $result = substr($result, 1);

            exit;
        }
}   
