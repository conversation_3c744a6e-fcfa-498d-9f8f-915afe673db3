<?php

namespace ChsOne\Integration\Controllers;

use ChsOne\Models\Society;

class BaseController extends \Phalcon\Mvc\Controller {
    /* public function initialize(){
      echo "initial";
      } */

    public function initialize() {
        global $di;
        $this->di = $di;
        //$this->_createsocietyDBConnection($soc_id);
    }

    
    
    
    
    public function beforeExecuteRoute() {
        global $config;
        $request = $this->request->get();
        if (isset($request['_url'])) {
            preg_match("/\/[a-zA-Z1-9]+.(xml|json|string)/", $request['_url'], $ext);
//            print_r($ext);exit();
            if (!empty($ext[1])) {
                $this->outputFormat = $ext[1];
            } else {
                $this->outputFormat = 'json';
            }
//            print_r(get_class_methods($this->response));exit();
        }
        try {
            $soc_id = $this->request->getPost('soc_id');

            $socPresent = $this->checkSociety($soc_id);

            if (!$socPresent) {
                throw new \Exception("Society is not present in our database.");
            } else {
                if (isset($soc_id) && !empty($soc_id)) {
                    $this->_createsocietyDBConnection($soc_id);
                }
            }
        } catch (\Exception $ex) {
            return $msg = $ex->getMessage();
        }
    }

    protected function _createsocietyDBConnection(&$soc_id) { 
        $socetyDBServiceobject = new \ChsOne\Components\Society\MultiDbFlow();
        $socetyDBServiceobject->initialize($this->di);
        $socetyDBServiceobject->setDbServSoc($soc_id);
    }

    /**
     * 
     * @param type $e
     * @return string
     */
    protected function handleException($e) {
//        print_r($e);exit();
        $invalidAccessTokenException = '\League\OAuth2\Server\Exception\InvalidAccessTokenException';
        $missingAccessTokenException = '\League\OAuth2\Server\Exception\MissingAccessTokenException';
        $invalidGrantTypeException = '\League\OAuth2\Server\Exception\InvalidGrantTypeException';
        $clientException = '\League\OAuth2\Server\Exception\ClientException';
        $expiredAccessTokenException = '\League\OAuth2\Server\Exception\ExpiredAccessTokenException';
        $insufficientScopeException = '\League\OAuth2\Server\Exception\InsufficientScopeException';
        

        if ($e instanceof $invalidAccessTokenException) {
            $arr['http_status'] = 401;
            $arr['status_code'] = (($e->getCode() > 222)) ? $e->getCode() : 401;
            $arr['text'] = 'Unauthorized';
            return $arr;
        } elseif ($e instanceof $missingAccessTokenException) {
            $arr['http_status'] = 400;
            $arr['status_code'] = ((($e->getCode() > 222))) ? $e->getCode() : 400;
            $arr['text'] = 'Bad Request';
            return $arr;
        } elseif ($e instanceof $invalidGrantTypeException) {
            $arr['http_status'] = 403;
            $arr['status_code'] = (($e->getCode() > 222)) ? $e->getCode() : 403;
            $arr['text'] = 'Forbidden';
            return $arr;
        } elseif ($e instanceof $clientException) {
            $arr['http_status'] = 401;
            $arr['status_code'] = (($e->getCode() > 222)) ? $e->getCode() : 400;
            $arr['text'] = 'Unauthorized';
            return $arr;
        } elseif ($e instanceof $expiredAccessTokenException) {
            $arr['http_status'] = 401;
            $arr['status_code'] = (($e->getCode() > 222)) ? $e->getCode() : 401;
            $arr['text'] = 'Unauthorized';
            return $arr;
        } elseif ($e instanceof $insufficientScopeException) {
            $arr['http_status'] = 403;
            $arr['status_code'] = (($e->getCode() > 222)) ? $e->getCode() : 400;
            $arr['text'] = 'Unauthorized';
            return $arr;
        }
        elseif($e->getCode() == 404){
            $arr['http_status'] = 404;
            $arr['status_code'] = (($e->getCode() > 222)) ? $e->getCode() : 404;
            $arr['text'] = 'Unauthorized';
            return $arr;
        }
//        elseif($e->getCode() == 500){
//            $arr['http_status'] = 500;
//            $arr['status_code'] = (($e->getCode() > 222)) ? $e->getCode() : 500;
//            $arr['text'] = 'Internal Server Error';
//            return $arr;
//        }
        
    }

    public function handleOwnerType($required_owner, $resource) {
        $owner_type = $resource->getOwnerType();
        if ($required_owner != $owner_type) {
            throw new \League\OAuth2\Server\Exception\InvalidAccessTokenException("Access Not Allowed");
        }
    }

    public function setSuccessResponse($message, $data, $status = 200, $header_code = 200) {
//        echo $message;exit();
        $data['data'] = $data;
        $data['app'] = ['version'=>'v1', 'app'=>'CHSONE'];
        $data['error'] = FALSE;
        $data['status'] = 'OK';
        $data['status_code'] = $status;
        $data['header_code'] = $header_code;
        $data['message'] = $message;
        return $this->setResponse($data);
    }

    public function setErrorResponse($e) {
//        print_r($e->getCode());exit();
        $exceptionDetails = $this->handleException($e);
//        print_r($exceptionDetails);exit();
        if (!isset($exceptionDetails['status_code']) || empty($exceptionDetails['status_code'])) {
            $exceptionDetails['status_code'] = 400;
        }
        if (!isset($exceptionDetails['text']) || empty($exceptionDetails['text'])) {
            $exceptionDetails['text'] = 'Bad Request';
        }
        $body['meta'] = [
            'error' => TRUE,
            'status_code' => $exceptionDetails['status_code'],
            'message' => $e->getMessage()
        ];
        return $this->response
                        ->setStatusCode($exceptionDetails['http_status'], $exceptionDetails['text'])
                        ->setContentType('application/json')
                        ->setJsonContent([
                            'app' => array('version' => 'v1', 'app' => 'CHSONE'),
                            'error' => TRUE,
                            'status_code' => $exceptionDetails['status_code'],
                            'message' => $e->getMessage()
        ]);
    }

    public function societyPresence($soc_id) {
	
	if(!is_numeric($soc_id)) {
            throw new \League\OAuth2\Server\Exception\MissingAccessTokenException("Invalid society id.", 400);
        }
        if (!isset($soc_id) || empty($soc_id)) {
            throw new \League\OAuth2\Server\Exception\MissingAccessTokenException("Society Id is Missing.", 400);
        } else {
            $checkSociety = $this->checkSociety($soc_id);
            if (!$checkSociety) {
                throw new \League\OAuth2\Server\Exception\InvalidAccessTokenException('Society is not available in our database. Please contact CHSONE', 400);
            }
        }
    }

    protected function checkSociety($soc_id) {
        $societyCnt = Society::count('soc_id = "' . $soc_id . ' AND status = 1"');
        if ($societyCnt < 1) {
            return false;
        } else {
            return true;
        }
    }

    //function defination to convert array to xml
    protected function array_to_xml($array, &$xml_user_info) {
//        print_r($array);exit();
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                if (!is_numeric($key)) {
                    $subnode = $xml_user_info->addChild("$key");
                    $this->array_to_xml($value, $subnode);
                } else {
                    $subnode = $xml_user_info->addChild("item$key");
                    $this->array_to_xml($value, $subnode);
                }
            } else {
                $xml_user_info->addChild("$key", htmlspecialchars("$value"));
            }
        }
        return $xml_user_info;
    }
    
    
    protected function setResponse($data){
//        print_r($data);exit();
        switch ($this->outputFormat) {
            case 'json':
                return $this->response
                                ->setContentType('application/json')
                                ->setStatusCode($data['header_code'])
                                ->setJsonContent([
                                    'app' => $data['app'],
                                    'status_code' => $data['status_code'],
                                    'message' => $data['message'],
                                    "data" => $data['data']
                ]);
                break;
            case 'xml':
                $xml_user_info = new \SimpleXMLElement("<?xml version=\"1.0\"?><data></data>");
                //function call to convert array to xml
                $this->array_to_xml($data['data'], $xml_user_info);
                $xml_file = $xml_user_info->asXML();
//                print_r(array_walk_recursive((array('error' => false) + $data), array($xml, 'addChild')));
//                exit();
                return $this->response
                                ->setContentType('text/xml')
                                ->setContent($xml_file);
                break;
            case 'string':
                $this->response->setHeader('Content-Type', 'text/plain');
                $this->response->setStatusCode($data['header_code']);
                return $this->response->setContent(serialize(array('error' => false) + $data));
                break;
        }// end of switch
    }
    
    
    public function checkValideDateFormat($date, $name){
        if (!preg_match("/^(0[1-9]|[1-2][0-9]|3[0-1])\/(0[1-9]|1[0-2])\/[0-9]{4}$/",$date))
        {
            throw new \League\OAuth2\Server\Exception\MissingAccessTokenException("Invalid ".$name." date format.");
        }
    }
    
    public function getMembers($soc_id, $user_id){
        $condition = 'soc_id = "'.$soc_id.'" AND user_id = "'.$user_id.'"';
//        echo $condition;exit();
        $members = \ChsOne\Models\MembersMaster::find(array('conditions'=>$condition, 'columns'=>'id as member_id'));
        $member_arr = array();
        foreach($members as $member){
            array_push($member_arr,$member->member_id);
        }
//        print_r($member_arr);exit();
        return $member_arr;
    }
    
    public function getMemberUnits($soc_id, $user_id){
        $condition = 'soc_id = "'.$soc_id.'" AND fk_unit_id !=null AND user_id = "'.$user_id.'"';
//        echo $condition;exit();
        $members = \ChsOne\Models\MembersMaster::find(array('conditions'=>$condition, 'columns'=>'id as fk_unit_id'));
        $member_arr = array();
        foreach($members as $member){
            array_push($member_arr,$member->member_id);
        }
//        print_r($member_arr);exit();
        return $member_arr;
    }
    
    
    public function connectDB($soc_id){
        $this->_createsocietyDBConnection($soc_id);
    }
    
    public function getToken($request) {
        $token = ($request->getQuery('token')) ? $request->getQuery('token') : $this->request->getHeaders()['Token'];
        $token = ($token) ? $token : $request->getPut('token');
        if (!isset($token) || empty($token)) {
           return false;
        }
        // remove special characters from token
        return str_replace("'", '', preg_replace('/[^a-zA-Z0-9\']/', '', $token));
    }
    
    public function customSetErrorResponse($message, $statusCode = 400) {
        $data['data'] = $data;
        $data['app'] = ['version' => "v1", 'app' => $this->config->app_details['app_name']];
        $data['error'] = $message;
        $data['status'] = 'error';
        $data['message'] = $message;
        if ($statusCode == 400) {
            return $this->response
                            ->setStatusCode($statusCode)
                            ->setContentType('application/json')
                            ->setJsonContent([
                                'app' => array('version' => "v1", 'name' => $this->config->app_details['app_name']),
                                'error' => true,
                                'status_code' => $statusCode,
                                'message' => $message
            ]);
        } elseif ($statusCode == 422) {
            return $this->response
                            ->setStatusCode($statusCode)
                            ->setContentType('application/json')
                            ->setJsonContent([
                                'app' => array('version' => "v1", 'name' => $this->config->app_details['app_name']),
                                'error' => true,
                                'status_code' => $statusCode,
                                'messages' => $message
            ]);
        } else {
            return $this->response
                            ->setStatusCode($statusCode)
                            ->setContentType('application/json')
                            ->setJsonContent([
                                'app' => array('version' => "v1", 'name' => $this->config->app_details['app_name']),
                                'status_code' => $statusCode,
                                'errors' => $message
            ]);
        }
    } 
    
    public function validationResponse($errors, $statusCode = 400) {
        return $this->response
                    ->setStatusCode($statusCode)
                    ->setContentType('application/json')
                    ->setJsonContent([
                        'app' => array('version' => "v1", 'name' => $this->config->app_details['app_name']),
                        'error' => true,
                        'status_code' => $statusCode,
                        'messages' => $errors
            ]);
    }
    
    /**
     * Clean Inputs
     * EG - $this->_request = $this->cleanInputs($_GET);
     * EG - $this->_request = $this->cleanInputs($_POST);
     * @param type $data
     * @return type
     */
    public function cleanInputs($data) {
        $clean_input = array();
        if (is_array($data)) {
            foreach ($data as $k => $v) {
                $clean_input[$k] = $this->cleanInputs($v);
            }
        } else {
            if (get_magic_quotes_gpc()) {
                $data = trim(stripslashes($data));
            }
            $data = htmlspecialchars(strip_tags($data));
            $clean_input = trim($data);
        }

        return $clean_input;
    }
    
}
