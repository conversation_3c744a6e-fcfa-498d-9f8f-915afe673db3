<?php
/**
 * This class file is a Module used to access ResidentAPI
 *
 * PHP versions 5.5.9
 *
 * Project name CHSONE
 * @version 1: residentapi/users/Module.php 2015-08-19 $
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category Module controller
 * <AUTHOR> <Futurescape Technologies>
 * @since File available since Release 1.0
 */
/**
 * @package ChsOne\Integration
 */
namespace ChsOne\Integration;

use \Phalcon\Loader,
\Phalcon\Mvc\Dispatcher,
\Phalcon\Mvc\View,
\Phalcon\Mvc\ModuleDefinitionInterface;

class Module implements ModuleDefinitionInterface
{
	/**
	 *
	 * @var string
	 * @staticvar $version
	 */
	public static $version='';

	/**
	 * This function is used to set the module version.
	 *
	 * @method setModuleVersion
	 * @access private
	 * @static $version
	 * @uses \Phalcon\Mvc\Router
	 */
private function setModuleVersion() {
		$router = new \Phalcon\Mvc\Router();
		$uri = $router->getRewriteUri();
                $uri = str_replace('/chsone/', '', $uri);
		$uri = explode('/', $uri);               
		self::$version = $uri[2];
	}
	/**
	 * This method autoload registered namespaces.
	 * @method registerAutoloaders
	 * @access public
	 * @param \Phalcon\DiInterface $di DEFAULT NULL
	 */
    public function registerAutoloaders(\Phalcon\DiInterface $di = null)
    { 
        $this->setModuleVersion();
    	$document_root = $_SERVER['DOCUMENT_ROOT'];
		$loader = new \Phalcon\Loader();
                $a = array(
                    //api namespaces
                    'ChsOne\Integration\Controllers' => realpath(dirname(__FILE__)) . '/'.(self::$version).'/controllers/',
                    'ChsOne\Society\Controllers' => __DIR__.'/../../app/society/controllers',
                    'ChsOne\SocietySetup\Controllers' => __DIR__.'/../../app/societySetup/controllers',
                    //common namespaces
    //			'ChsOne\SocietySetup\Controllers' => realpath(dirname(dirname(__FILE__))) . '/societySetup/'.(self::$version).'/controllers/',
                    //'League\OAuth2\Server\Authorization' => '/var/www/html/chsone_admin/vendor/league/oauth2-server/src/League/OAuth2/Server'//realpath(dirname(dirname(__FILE__))). "/../vendor/league/oauth2-server/src/League/OAuth2/Server"
                    //'ChsOne\Models' => realpath(dirname($document_root)) . '/common/models/',
                    'ChsOne\Components' => __DIR__ . '/../../common/libraries',
                    'ChsOne\Helper' => __DIR__ . '/../../common/helper',
                    'ChsOne\Components\IncomeTracker\Invoicegenerate\Listeners' => __DIR__ . '/../../common/libraries/IncomeTracker/Invoicegenerate',
                    'ChsOne\Components\IncomeTracker' => __DIR__ . "/../../common/libraries/IncomeTracker",
                    'ChsOne\Components\IncomeTracker\Tracking\Listeners' => __DIR__ . '/../../common/libraries/IncomeTracker/tracking',
                    'ChsOne\Components\IncomeTracker\Invoicing\Listeners' => __DIR__ . '/../../common/libraries/IncomeTracker/invoicing',
                    'ChsOne\Components\IncomeTracker\Setting\Listeners' => __DIR__ . '/../../common/libraries/IncomeTracker/setting',
                    'ChsOne\Helper' => __DIR__ . '/../../common/helper',
                    'ChsOne\IncomeTracker\Forms'       => __DIR__ . '/../../app/incometracker/forms/',
                    
                    'ChsOne\Components\Setup' => __DIR__ . "/../../common/libraries/setup",
                    'ChsOne\Components\Setup\Listeners' => __DIR__ . "/../../common/libraries/Setup/Listeners",
		);

                $loader->registerNamespaces($a);
//     echo '<pre>'; print_r($loader->getNamespaces($a));exit();
		$loader->register();
		//$this->oauth2();
    }

    /**
     * This method sets service (view) for society module
     * @method registerServices
     * @access public
     * @param resource $di
     *
     */
    public function registerServices(\Phalcon\DiInterface $di)
    {
		/**
		 * Setting up the view component
		 */
		$di->set('view', function() {
			$view = new \Phalcon\Mvc\View();
			//$view->disable();
			return $view;
		});
    }

    public function oauth2() {
    	//echo 'acoustics';exit();
    }
}
