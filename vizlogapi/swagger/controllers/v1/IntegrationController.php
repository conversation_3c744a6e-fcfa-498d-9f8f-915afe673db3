<?php

class IntegrationController {

    /**
     * @SWG\Get(
     *     path="/societylist",
     *     summary="List of Societies",
     *     tags={"FSAdmin"},
     *     description="List of Societies",
     *     operationId="getsocietylist",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="is_paging",    
     *         in="query",
     *         description="If paging is needed set is_paging to 1",
     *         required=false,
     *         type="integer",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="page",    
     *         in="query",
     *         description="If paging is required, page number can be specified",
     *         required=false,
     *         type="integer",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function getSocietyListAction() {
        
    }
    
    /**
     * @SWG\Put(
     *     path="/unitvpa",
     *     summary="List of Societies",
     *     tags={"FSAdmin"},
     *     description="List of Societies",
     *     operationId="societies",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="soc_id",    
     *         description="Society id",
     *         required=true,
     *         type="integer",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="unit_id",    
     *         description="Unit id",
     *         required=true,
     *         type="integer",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="vpa",    
     *         description="Virtual Payment Account",
     *         required=true,
     *         type="string",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function putUnitVpaAction() {
        
    }

    /**
     * @SWG\Get(
     *     path="/dues/{vpa}",
     *     summary="Get deues of unit by VPA",
     *     tags={"FSAdmin"},
     *     description="Get total due of unit by passing VPA of unit",
     *     operationId="FSAdmin",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="vpa",    
     *         description="Virtual payment account",
     *         required=true,
     *         type="string",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function getduesAction() {
        
    }
    
    /**
     * @SWG\Get(
     *     path="/memberdetails/{vpa}",
     *     summary="Get details of member",
     *     tags={"FSAdmin"},
     *     description="Get details of member of unit by VPA",
     *     operationId="FSAdmin",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="vpa",    
     *         description="Virtual payment account",
     *         required=true,
     *         type="string",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function getmemberdetailsAction() {
    
    }

    /**
     * @SWG\Put(
     *     path="/societycode",
     *     summary="Update unique code of society",
     *     tags={"FSAdmin"},
     *     description="Update unique code of society from FSAdmin",
     *     operationId="FSAdmin",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="soc_id",    
     *         description="Society id",
     *         required=true,
     *         type="integer",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="soc_code",    
     *         description="Unique code for society",
     *         required=true,
     *         type="string",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function putsocietycodeAction() {
        
    }
    
    /**
     * @SWG\Get(
     *     path="/buildings/{soc_id}",
     *     summary="Get list of buildings in society",
     *     tags={"Integration"},
     *     description="Update unique code of society from FSAdmin",
     *     operationId="Integration",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="soc_id",    
     *         description="Society id",
     *         required=true,
     *         type="integer",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function getBuildingsAction() {
        
    }
    
    /**
     * @SWG\Get(
     *     path="/unitstatus/{soc_id}/{user_id}",
     *     summary="Get user's related units from society",
     *     tags={"Integration"},
     *     description="Get list of all units belongs to user as well as all units which have been claimed by user",
     *     operationId="Integration",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="soc_id",    
     *         description="Society id",
     *         required=true,
     *         type="integer",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="user_id",    
     *         description="User id",
     *         required=true,
     *         type="integer",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function getUnitStatusAction() {
        
    }
    
    /**
     * @SWG\Get(
     *     path="/relatedunits/{soc_id}/{email_id}/{mobile_number}",
     *     summary="Get user's related units from society",
     *     tags={"Integration"},
     *     description="Get list of all units belongs to user by email id or contact number",
     *     operationId="Integration",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="soc_id",    
     *         description="Society id",
     *         required=true,
     *         type="integer",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="email_id",    
     *         description="User's email id",
     *         required=true,
     *         type="string",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="mobile_number",    
     *         description="User's mobile number",
     *         required=true,
     *         type="number",
     *         in="path",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function getRelatedUnitsAction() {
        
    }
    
     /**
     * @SWG\Get(
     *     path="/contraentry",
     *     summary="This API is yet to be build",
     *     tags={"Integration"},
     *     description="Get list of all units belongs to user by email id or contact number",
     *     operationId="Integration",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="soc_id",    
     *         description="Society id",
     *         required=true,
     *         type="integer",
     *         in="query",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="email_id",    
     *         description="User's email id",
     *         required=true,
     *         type="string",
     *         in="query",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="mobile_number",    
     *         description="User's mobile number",
     *         required=true,
     *         type="number",
     *         in="query", 
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function postcontraentryAction() {
        
    }
    
     /**
     * @SWG\Post(
     *     path="/generateaccesstoken",
     *     summary="Generate access token",
     *     tags={"Integration"},
     *     description="Generate access token to access data from application",
     *     operationId="Integration",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="grant_type", 
     *         description="Grant Type",
     *         required=true,
     *         in="formData", 
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="client_id", 
     *         description="Client id",
     *         required=true,
     *         type="string",
     *         in="formData", 
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="client_secret", 
     *         description="Client secret",
     *         required=true,
     *         type="string",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function postgenerateaccesstokenAction() {
        
    }
    
    /**
     * @SWG\Post(
     *     path="/payment",
     *     summary="Initiate payment of maintenance bill",
     *     tags={"FSAdmin"},
     *     description="Start payment transaction of member against unit to bank",
     *     operationId="FSAdmin",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),    
     *     @SWG\Parameter(
     *         name="received_from", 
     *         description="Name of person who is paying",
     *         required=true,
     *         in="formData", 
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="payment_amount", 
     *         description="Amount to be paid",
     *         required=true,
     *         type="string",
     *         in="formData", 
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="payment_date", 
     *         description="Date of payment in dd/mm/yyyy format",
     *         required=true,
     *         type="string",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="bank_account", 
     *         description="ID of society bank account in which payment has to be done",
     *         required=true,
     *         type="string",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="transaction_reference", 
     *         description="Reference code of transaction",
     *         required=true,
     *         type="string",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="unit_id", 
     *         description="VPA of unit",
     *         required=true,
     *         type="string",
     *         in="formData",
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function postPaymentAction() {
        
    }
    
    /**
     * @SWG\Put(
     *     path="/confirmation",
     *     summary="Confirm initiated payment of maintenance bill",
     *     tags={"FSAdmin"},
     *     description="Confirm initiated payment transaction",
     *     operationId="FSAdmin",
     *     consumes={"application/xml", "application/json"},
     *     produces={"application/xml", "application/json"},
     *     @SWG\Parameter(
     *         name="token",    
     *         in="header",
     *         description="Access Token",
     *         required=true,
     *         type="string",
     *         collectionFormat="multi"
     *     ),
     *     @SWG\Parameter(
     *         name="payment_token", 
     *         description="Unique token to identify payment request",
     *         required=true,
     *         type="string",
     *         in="formData", 
     *         collectionFormat="multi"
     *     ),
     *      @SWG\Response(
     *         response=200,
     *         description="Successful operation",
     *         @SWG\Schema(
     *             type="array",
     *         ),
     *     ),
     *     @SWG\Response(
     *         response="400",
     *         description="Bad Request (Parameters Missing/Invalid Parameters)",
     *     ),
     *      @SWG\Response(
     *         response="401",
     *         description="Unauthorized (Invalid data)",
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Forbidden",
     *     )
     * )
     */
    public function putConfirmationAction() {
        
    }
    
}
