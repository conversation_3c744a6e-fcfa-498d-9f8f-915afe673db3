{"name": "zircote/swagger-php", "type": "library", "license": "Apache2", "bin": ["bin/swagger"], "description": "Swagger-PHP - Generate interactive documentation for your RESTful API using phpdoc annotations", "keywords": ["json", "rest", "api", "service discovery"], "homepage": "https://github.com/zircote/swagger-php/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.zircote.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://bfanger.nl"}], "config": {"bin-dir": "bin"}, "require": {"php": ">=5.4.0", "doctrine/annotations": "*", "symfony/finder": "*"}, "autoload": {"psr-4": {"Swagger\\": "src"}, "files": ["src/functions.php"]}, "require-dev": {"zendframework/zend-form": "<2.8", "squizlabs/php_codesniffer": ">=2.7", "phpunit/phpunit": ">=4.8"}, "autoload-dev": {"psr-4": {"SwaggerTests\\": "tests/"}}}